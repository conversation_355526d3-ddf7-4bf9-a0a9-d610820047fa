# 🚀 Vietnamese Classroom Management System - PHP Version

## 📋 **Tổng quan**

Hệ thống quản lý lớp học tiếng Việt được phát triển bằng PHP + MySQL, chuyển đổi từ phiên bản Flask + MongoDB.

### **✨ Tính năng chính**

- **👥 Quản lý người dùng**: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Gi<PERSON><PERSON> viên
- **🏫 Quản lý lớp học**: <PERSON><PERSON><PERSON>, s<PERSON><PERSON>, x<PERSON><PERSON> lớp học
- **👨‍🎓 Quản lý học sinh**: <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>, x<PERSON><PERSON> học sinh
- **📅 Quản lý lịch học**: <PERSON><PERSON><PERSON> lịch theo tuần
- **✅ Điểm danh**: <PERSON> dõi tình hình học tập
- **💰 Quản lý tài chính**: <PERSON><PERSON> chi, quyên góp
- **📊 Báo cáo**: <PERSON>h<PERSON><PERSON> kê và xuất Excel

---

## 🛠️ **C<PERSON><PERSON> nghệ sử dụng**

- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Frontend**: Tailwind CSS, JavaScript
- **Authentication**: PHP Sessions
- **Security**: PDO, CSRF Protection

---

## 📦 **Cài đặt**

### **1. Yêu cầu hệ thống**
- PHP 8.0 hoặc cao hơn
- MySQL 8.0 hoặc cao hơn
- Web server (Apache/Nginx)
- Composer (tùy chọn)

### **2. Cài đặt cơ sở dữ liệu**
```sql
-- Import database schema
mysql -u root -p < database/schema.sql
```

### **3. Cấu hình**
```php
// config/database.php
$_ENV['DB_HOST'] = 'localhost';
$_ENV['DB_NAME'] = 'vietnamese_classroom';
$_ENV['DB_USER'] = 'root';
$_ENV['DB_PASS'] = 'your_password';
```

### **4. Chạy hệ thống**
```bash
# Với PHP built-in server
php -S localhost:8000

# Hoặc cấu hình với Apache/Nginx
```

---

## 🔐 **Đăng nhập mặc định**

- **Username**: admin
- **Password**: admin123

---

## 🧪 **Kiểm tra hệ thống**

Chạy script test để kiểm tra:
```bash
php test_system.php
```

---

## 📁 **Cấu trúc dự án**

```
├── 📁 config/          # Cấu hình database
├── 📁 includes/        # Authentication & helpers
├── 📁 models/          # Database models
├── 📁 pages/           # Application pages
├── 📁 auth/            # Login system
├── 📁 database/        # Database schema
├── 📄 index.php        # Main entry point
├── 📄 test_system.php  # System test script
└── 📄 README.md        # Documentation
```

---

## 🚀 **Triển khai**

### **Shared Hosting (cPanel)**
1. Upload files qua File Manager
2. Import database qua phpMyAdmin
3. Cấu hình database connection
4. Truy cập domain

### **VPS/Dedicated Server**
1. Clone repository
2. Cấu hình web server
3. Setup database
4. Configure SSL

---

## 🔧 **Bảo trì**

- **Backup**: Sao lưu database định kỳ
- **Updates**: Cập nhật PHP và MySQL
- **Security**: Kiểm tra logs và permissions
- **Performance**: Optimize database queries

---

## 📞 **Hỗ trợ**

- **Website**: https://qllhttbb.vn/
- **Email**: <EMAIL>

---

## 📄 **License**

© 2025 QLLHTTBB. All rights reserved.
