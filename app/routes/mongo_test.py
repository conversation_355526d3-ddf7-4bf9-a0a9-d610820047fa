from flask import Blueprint, render_template, jsonify, request, flash, redirect, url_for
from flask_login import login_required, current_user
from app.models.mongo_models import MongoUser, MongoClass, MongoStudent, MongoSchedule, MongoAttendance
from datetime import datetime

bp = Blueprint('mongo_test', __name__)

@bp.route('/mongo/test')
@login_required
def test_mongo():
    """Test MongoDB connection and display data"""
    try:
        # Get statistics
        stats = {
            'users': MongoUser.objects.count(),
            'classes': MongoClass.objects.count(),
            'students': MongoStudent.objects.count(),
            'schedules': MongoSchedule.objects.count(),
            'attendance': MongoAttendance.objects.count()
        }
        
        # Get sample data
        users = MongoUser.objects.limit(5)
        classes = MongoClass.objects.limit(5)
        students = MongoStudent.objects.limit(5)
        schedules = MongoSchedule.objects.limit(5)
        
        return render_template('mongo_test.html', 
                             title='MongoDB Test',
                             stats=stats,
                             users=users,
                             classes=classes,
                             students=students,
                             schedules=schedules)
    except Exception as e:
        flash(f'MongoDB Error: {str(e)}', 'error')
        return redirect(url_for('main.dashboard'))

@bp.route('/mongo/users')
@login_required
def mongo_users():
    """List MongoDB users"""
    try:
        users = MongoUser.objects.all()
        return jsonify({
            'success': True,
            'count': len(users),
            'users': [{
                'id': str(user.id),
                'username': user.username,
                'full_name': user.full_name,
                'email': user.email,
                'role': user.role,
                'is_active': user.is_active,
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None
            } for user in users]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/mongo/classes')
@login_required
def mongo_classes():
    """List MongoDB classes"""
    try:
        classes = MongoClass.objects.all()
        return jsonify({
            'success': True,
            'count': len(classes),
            'classes': [{
                'id': str(cls.id),
                'name': cls.name,
                'description': cls.description,
                'manager': cls.manager.full_name if cls.manager else None,
                'is_active': cls.is_active,
                'created_at': cls.created_at.strftime('%Y-%m-%d %H:%M:%S') if cls.created_at else None
            } for cls in classes]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/mongo/students')
@login_required
def mongo_students():
    """List MongoDB students"""
    try:
        students = MongoStudent.objects.all()
        return jsonify({
            'success': True,
            'count': len(students),
            'students': [{
                'id': str(student.id),
                'student_code': student.student_code,
                'full_name': student.full_name,
                'class_name': student.class_ref.name if student.class_ref else None,
                'parent_name': student.parent_name,
                'parent_phone': student.parent_phone,
                'is_active': student.is_active,
                'created_at': student.created_at.strftime('%Y-%m-%d %H:%M:%S') if student.created_at else None
            } for student in students]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/mongo/schedules')
@login_required
def mongo_schedules():
    """List MongoDB schedules"""
    try:
        schedules = MongoSchedule.objects.all()
        return jsonify({
            'success': True,
            'count': len(schedules),
            'schedules': [{
                'id': str(schedule.id),
                'class_name': schedule.class_ref.name if schedule.class_ref else None,
                'teacher_name': schedule.teacher.full_name if schedule.teacher else None,
                'day_of_week': schedule.day_of_week,
                'session': schedule.session,
                'start_time': schedule.start_time,
                'end_time': schedule.end_time,
                'subject': schedule.subject,
                'room': schedule.room,
                'week_number': schedule.week_number,
                'is_active': schedule.is_active,
                'created_at': schedule.created_at.strftime('%Y-%m-%d %H:%M:%S') if schedule.created_at else None
            } for schedule in schedules]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/mongo/create_test_data', methods=['POST'])
@login_required
def create_test_data():
    """Create test data in MongoDB"""
    try:
        # Create test user
        test_user = MongoUser(
            username=f'test_user_{int(datetime.now().timestamp())}',
            email=f'test_{int(datetime.now().timestamp())}@test.com',
            full_name='Test User MongoDB',
            role='teacher',
            is_active=True
        )
        test_user.set_password('test123')
        test_user.save()
        
        # Create test class
        test_class = MongoClass(
            name=f'Test Class {int(datetime.now().timestamp())}',
            description='Test class created via MongoDB',
            manager=MongoUser.objects(role='admin').first(),
            is_active=True
        )
        test_class.save()
        
        # Create test student
        test_student = MongoStudent(
            student_code=f'TEST{int(datetime.now().timestamp())}',
            full_name='Test Student MongoDB',
            class_ref=test_class,
            parent_name='Test Parent',
            parent_phone='0123456789',
            is_active=True
        )
        test_student.save()
        
        return jsonify({
            'success': True,
            'message': 'Test data created successfully',
            'data': {
                'user_id': str(test_user.id),
                'class_id': str(test_class.id),
                'student_id': str(test_student.id)
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/mongo/stats')
@login_required
def mongo_stats():
    """Get MongoDB statistics"""
    try:
        stats = {
            'users': {
                'total': MongoUser.objects.count(),
                'admin': MongoUser.objects(role='admin').count(),
                'manager': MongoUser.objects(role='manager').count(),
                'teacher': MongoUser.objects(role='teacher').count(),
                'active': MongoUser.objects(is_active=True).count()
            },
            'classes': {
                'total': MongoClass.objects.count(),
                'active': MongoClass.objects(is_active=True).count()
            },
            'students': {
                'total': MongoStudent.objects.count(),
                'active': MongoStudent.objects(is_active=True).count()
            },
            'schedules': {
                'total': MongoSchedule.objects.count(),
                'active': MongoSchedule.objects(is_active=True).count(),
                'current_week': MongoSchedule.objects(week_number=MongoSchedule.get_current_week()).count()
            }
        }
        
        return jsonify({
            'success': True,
            'stats': stats,
            'current_week': MongoSchedule.get_current_week()
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
