from datetime import datetime
from mongoengine import Document, <PERSON>Field, DateTimeField, BooleanField, IntField, ReferenceField, ListField, EmbeddedDocument, EmbeddedDocumentField, DateField, FloatField
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

class MongoUser(Document, UserMixin):
    """MongoDB User Model"""
    username = String<PERSON>ield(max_length=64, required=True, unique=True)
    email = StringField(max_length=120, required=True, unique=True)
    password_hash = StringField(max_length=128)
    full_name = StringField(max_length=100, required=True)
    phone = StringField(max_length=20)
    role = StringField(max_length=20, required=True, choices=['admin', 'manager', 'teacher', 'user'])
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'users',
        'indexes': ['username', 'email', 'role']
    }
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'
    
    def is_manager(self):
        return self.role == 'manager'
    
    def is_teacher(self):
        return self.role == 'teacher'
    
    def is_user(self):
        return self.role == 'user'
    
    def __str__(self):
        return f'<MongoUser {self.username}>'

class MongoClass(Document):
    """MongoDB Class Model"""
    name = StringField(max_length=50, required=True)
    description = StringField()
    manager = ReferenceField(MongoUser)
    teachers = ListField(ReferenceField(MongoUser))
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'classes',
        'indexes': ['name', 'manager', 'is_active']
    }
    
    def __str__(self):
        return f'<MongoClass {self.name}>'

class MongoStudent(Document):
    """MongoDB Student Model"""
    student_code = StringField(max_length=20, required=True, unique=True)
    full_name = StringField(max_length=100, required=True)
    date_of_birth = DateField()
    address = StringField()
    parent_name = StringField(max_length=100)
    parent_phone = StringField(max_length=20)
    profile_url = StringField(max_length=500)
    class_ref = ReferenceField(MongoClass)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'students',
        'indexes': ['student_code', 'full_name', 'class_ref']
    }
    
    def __str__(self):
        return f'<MongoStudent {self.student_code} - {self.full_name}>'

class MongoSchedule(Document):
    """MongoDB Schedule Model"""
    class_ref = ReferenceField(MongoClass, required=True)
    teacher = ReferenceField(MongoUser, required=True)
    day_of_week = IntField(required=True, min_value=1, max_value=7)
    session = StringField(max_length=20, required=True, choices=['morning', 'afternoon', 'evening'])
    start_time = StringField(required=True)  # Store as "HH:MM" format
    end_time = StringField(required=True)
    subject = StringField(max_length=100)
    room = StringField(max_length=50)
    week_number = StringField(max_length=10, required=True)  # Format: 2025-W25
    week_created = StringField(max_length=10)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'schedules',
        'indexes': [
            ('week_number', 'day_of_week', 'teacher'),
            ('class_ref', 'week_number'),
            'teacher',
            'is_active'
        ]
    }
    
    @staticmethod
    def get_current_week():
        """Get current week in YYYY-WXX format"""
        now = datetime.now()
        year, week, _ = now.isocalendar()
        return f"{year}-W{week:02d}"
    
    @staticmethod
    def get_next_week():
        """Get next week in YYYY-WXX format"""
        from datetime import timedelta
        next_week_date = datetime.now() + timedelta(weeks=1)
        year, week, _ = next_week_date.isocalendar()
        return f"{year}-W{week:02d}"
    
    def __str__(self):
        return f'<MongoSchedule {self.class_ref.name} - {self.teacher.full_name} - Week {self.week_number}>'

class MongoStudentSchedule(Document):
    """MongoDB Student-Schedule Association"""
    student = ReferenceField(MongoStudent, required=True)
    schedule = ReferenceField(MongoSchedule, required=True)
    enrolled_date = DateTimeField(default=datetime.utcnow)
    is_active = BooleanField(default=True)
    
    meta = {
        'collection': 'student_schedules',
        'indexes': [
            ('student', 'schedule'),
            'student',
            'schedule',
            'is_active'
        ]
    }
    
    @classmethod
    def enroll_student(cls, student_id, schedule_id):
        """Enroll a student in a schedule"""
        existing = cls.objects(
            student=student_id,
            schedule=schedule_id,
            is_active=True
        ).first()
        
        if existing:
            return existing
        
        enrollment = cls(
            student=student_id,
            schedule=schedule_id,
            is_active=True
        )
        enrollment.save()
        return enrollment
    
    @classmethod
    def unenroll_student(cls, student_id, schedule_id):
        """Unenroll a student from a schedule"""
        enrollment = cls.objects(
            student=student_id,
            schedule=schedule_id,
            is_active=True
        ).first()
        
        if enrollment:
            enrollment.is_active = False
            enrollment.save()
            return enrollment
        return None
    
    def __str__(self):
        return f'<MongoStudentSchedule {self.student.full_name} - {self.schedule.subject}>'

class MongoAttendance(Document):
    """MongoDB Attendance Model"""
    student = ReferenceField(MongoStudent, required=True)
    schedule = ReferenceField(MongoSchedule, required=True)
    date = DateField(required=True)
    status = StringField(max_length=20, required=True, choices=['present', 'absent', 'late', 'excused'])
    notes = StringField()
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'attendance',
        'indexes': [
            ('student', 'schedule', 'date'),
            'date',
            'status',
            'schedule'
        ]
    }
    
    def __str__(self):
        return f'<MongoAttendance {self.student.full_name} - {self.date} - {self.status}>'

class MongoEvent(Document):
    """MongoDB Event Model"""
    title = StringField(max_length=200, required=True)
    description = StringField()
    start_datetime = DateTimeField(required=True)
    end_datetime = DateTimeField()
    location = StringField(max_length=200)
    creator = ReferenceField(MongoUser, required=True)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'events',
        'indexes': ['start_datetime', 'creator', 'is_active']
    }
    
    def __str__(self):
        return f'<MongoEvent {self.title}>'

class MongoFinance(Document):
    """MongoDB Finance Model"""
    title = StringField(max_length=200, required=True)
    amount = FloatField(required=True)
    transaction_type = StringField(max_length=20, required=True, choices=['income', 'expense'])
    category = StringField(max_length=100)
    description = StringField()
    transaction_date = DateField(required=True)
    creator = ReferenceField(MongoUser, required=True)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'finances',
        'indexes': ['transaction_date', 'transaction_type', 'creator', 'is_active']
    }
    
    def __str__(self):
        return f'<MongoFinance {self.title} - {self.amount}>'

class MongoExpenseCategory(Document):
    """MongoDB Expense Category Model"""
    name = StringField(max_length=100, required=True, unique=True)
    description = StringField()
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'expense_categories',
        'indexes': ['name', 'is_active']
    }
    
    def __str__(self):
        return f'<MongoExpenseCategory {self.name}>'

class MongoExpense(Document):
    """MongoDB Expense Model"""
    title = StringField(max_length=200, required=True)
    amount = FloatField(required=True)
    category = ReferenceField(MongoExpenseCategory)
    description = StringField()
    expense_date = DateField(required=True)
    creator = ReferenceField(MongoUser, required=True)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'expenses',
        'indexes': ['expense_date', 'category', 'creator', 'is_active']
    }
    
    def __str__(self):
        return f'<MongoExpense {self.title} - {self.amount}>'

class MongoDonationAsset(Document):
    """MongoDB Donation Asset Model"""
    name = StringField(max_length=200, required=True)
    description = StringField()
    quantity = IntField(required=True, default=1)
    unit = StringField(max_length=50)
    received_date = DateField(required=True)
    donor_name = StringField(max_length=100)
    donor_contact = StringField(max_length=100)
    status = StringField(max_length=20, choices=['received', 'distributed', 'in_stock'], default='received')
    creator = ReferenceField(MongoUser, required=True)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'donation_assets',
        'indexes': ['received_date', 'status', 'creator', 'is_active']
    }
    
    def __str__(self):
        return f'<MongoDonationAsset {self.name} - {self.quantity} {self.unit}>'
