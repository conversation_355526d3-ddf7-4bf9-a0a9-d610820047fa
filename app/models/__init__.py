# Legacy SQL Models (keep for migration)
from .user import User
from .class_model import Class
from .student import Student
from .schedule import Schedule
from .student_schedule import StudentSchedule
from .attendance import Attendance
from .event import Event
from .finance import Finance
from .expense import Expense, ExpenseCategory, Budget
from .financial_transaction import FinancialTransaction, DonationAsset, DonationRecord

# New MongoDB Models
from .mongo_models import (
    MongoUser, MongoClass, MongoStudent, MongoSchedule,
    MongoStudentSchedule, MongoAttendance, MongoEvent,
    MongoFinance, MongoExpense, MongoExpenseCategory,
    MongoDonationAsset
)
