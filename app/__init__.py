from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON>gin<PERSON>ana<PERSON>
from flask_wtf.csrf import CSRFProtect
import mongoengine
from config import Config

# Initialize extensions
db = SQLAlchemy()  # Keep for migration period
migrate = Migrate()
login = LoginManager()
csrf = CSRFProtect()
login.login_view = 'auth.login'
login.login_message = 'Vui lòng đăng nhập để truy cập trang này.'

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)  # Keep for migration period
    migrate.init_app(app, db)
    login.init_app(app)
    csrf.init_app(app)  # Enable CSRF protection

    # Initialize MongoDB
    mongodb_settings = app.config.get('MONGODB_SETTINGS', {})
    if mongodb_settings:
        mongoengine.connect(**mongodb_settings)

    # Add CSRF token to template context
    @app.context_processor
    def inject_csrf_token():
        try:
            from flask_wtf.csrf import generate_csrf
            return dict(csrf_token=generate_csrf)
        except:
            return dict(csrf_token='')

    # User loader for Flask-Login
    @login.user_loader
    def load_user(user_id):
        # Try MongoDB first, fallback to SQL during migration
        try:
            from app.models.mongo_models import MongoUser
            return MongoUser.objects(id=user_id).first()
        except:
            from app.models.user import User
            return User.query.get(int(user_id))

    from app.routes import main, auth, admin, manager, teacher, user, finance, calendar, expense, financial, api, mongo_test
    app.register_blueprint(main.bp)
    app.register_blueprint(auth.bp, url_prefix='/auth')
    app.register_blueprint(admin.bp, url_prefix='/admin')
    app.register_blueprint(manager.bp, url_prefix='/manager')
    app.register_blueprint(teacher.bp, url_prefix='/teacher')
    app.register_blueprint(user.bp, url_prefix='/user')
    app.register_blueprint(finance.bp, url_prefix='/finance')
    app.register_blueprint(calendar.bp, url_prefix='/calendar')
    app.register_blueprint(expense.bp, url_prefix='/expense')
    app.register_blueprint(financial.bp)
    app.register_blueprint(api.bp)
    app.register_blueprint(mongo_test.bp, url_prefix='/mongo_test')

    return app

from app import models
