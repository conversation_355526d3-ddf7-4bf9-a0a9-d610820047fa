{% extends "base_tailwind.html" %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">🍃 MongoDB Connection Test</h1>
        <p class="text-gray-600">Testing MongoDB integration for Vietnamese Classroom Management System</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">Users</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ stats.users }}</p>
                </div>
                <div class="text-blue-500">
                    <i class="fas fa-users text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">Classes</h3>
                    <p class="text-3xl font-bold text-green-600">{{ stats.classes }}</p>
                </div>
                <div class="text-green-500">
                    <i class="fas fa-chalkboard text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
            <div class="flex items-center">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">Students</h3>
                    <p class="text-3xl font-bold text-yellow-600">{{ stats.students }}</p>
                </div>
                <div class="text-yellow-500">
                    <i class="fas fa-user-graduate text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
            <div class="flex items-center">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">Schedules</h3>
                    <p class="text-3xl font-bold text-purple-600">{{ stats.schedules }}</p>
                </div>
                <div class="text-purple-500">
                    <i class="fas fa-calendar text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500">
            <div class="flex items-center">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">Attendance</h3>
                    <p class="text-3xl font-bold text-red-600">{{ stats.attendance }}</p>
                </div>
                <div class="text-red-500">
                    <i class="fas fa-check-circle text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mb-8 flex flex-wrap gap-4">
        <button onclick="loadData('users')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-users mr-2"></i>Load Users
        </button>
        <button onclick="loadData('classes')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-chalkboard mr-2"></i>Load Classes
        </button>
        <button onclick="loadData('students')" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-user-graduate mr-2"></i>Load Students
        </button>
        <button onclick="loadData('schedules')" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-calendar mr-2"></i>Load Schedules
        </button>
        <button onclick="createTestData()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>Create Test Data
        </button>
        <button onclick="getStats()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-chart-bar mr-2"></i>Get Stats
        </button>
    </div>

    <!-- Data Display Area -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">MongoDB Data</h2>
        <div id="dataDisplay" class="bg-gray-50 rounded-lg p-4 min-h-96">
            <p class="text-gray-500 text-center">Click a button above to load data from MongoDB</p>
        </div>
    </div>

    <!-- Sample Data Tables -->
    {% if users %}
    <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Sample Users</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Username</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Full Name</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user in users %}
                    <tr>
                        <td class="px-4 py-2 text-sm text-gray-900">{{ user.username }}</td>
                        <td class="px-4 py-2 text-sm text-gray-900">{{ user.full_name }}</td>
                        <td class="px-4 py-2 text-sm">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {% if user.role == 'admin' %}bg-red-100 text-red-800
                                {% elif user.role == 'manager' %}bg-blue-100 text-blue-800
                                {% elif user.role == 'teacher' %}bg-green-100 text-green-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ user.role.title() }}
                            </span>
                        </td>
                        <td class="px-4 py-2 text-sm">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {% if user.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if user.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

<script>
function loadData(type) {
    const dataDisplay = document.getElementById('dataDisplay');
    dataDisplay.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-2xl text-gray-500"></i><p class="mt-2 text-gray-500">Loading...</p></div>';
    
    fetch(`/mongo_test/mongo/${type}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                dataDisplay.innerHTML = `
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">${type.charAt(0).toUpperCase() + type.slice(1)} (${data.count} records)</h3>
                    </div>
                    <pre class="bg-gray-800 text-green-400 p-4 rounded-lg overflow-auto text-sm">${JSON.stringify(data, null, 2)}</pre>
                `;
            } else {
                dataDisplay.innerHTML = `<div class="text-red-600">Error: ${data.error}</div>`;
            }
        })
        .catch(error => {
            dataDisplay.innerHTML = `<div class="text-red-600">Network Error: ${error.message}</div>`;
        });
}

function createTestData() {
    const dataDisplay = document.getElementById('dataDisplay');
    dataDisplay.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-2xl text-gray-500"></i><p class="mt-2 text-gray-500">Creating test data...</p></div>';
    
    fetch('/mongo_test/mongo/create_test_data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                dataDisplay.innerHTML = `
                    <div class="text-green-600 mb-4">
                        <i class="fas fa-check-circle mr-2"></i>${data.message}
                    </div>
                    <pre class="bg-gray-800 text-green-400 p-4 rounded-lg overflow-auto text-sm">${JSON.stringify(data, null, 2)}</pre>
                `;
            } else {
                dataDisplay.innerHTML = `<div class="text-red-600">Error: ${data.error}</div>`;
            }
        })
        .catch(error => {
            dataDisplay.innerHTML = `<div class="text-red-600">Network Error: ${error.message}</div>`;
        });
}

function getStats() {
    const dataDisplay = document.getElementById('dataDisplay');
    dataDisplay.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-2xl text-gray-500"></i><p class="mt-2 text-gray-500">Loading statistics...</p></div>';
    
    fetch('/mongo_test/mongo/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                dataDisplay.innerHTML = `
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">MongoDB Statistics</h3>
                        <p class="text-sm text-gray-600">Current Week: ${data.current_week}</p>
                    </div>
                    <pre class="bg-gray-800 text-green-400 p-4 rounded-lg overflow-auto text-sm">${JSON.stringify(data.stats, null, 2)}</pre>
                `;
            } else {
                dataDisplay.innerHTML = `<div class="text-red-600">Error: ${data.error}</div>`;
            }
        })
        .catch(error => {
            dataDisplay.innerHTML = `<div class="text-red-600">Network Error: ${error.message}</div>`;
        });
}
</script>
{% endblock %}
