#!/usr/bin/env python3
"""
Migration script from Flask + MongoDB/PostgreSQL to PHP + MySQL
Vietnamese Classroom Management System
"""

import os
import sys
import json
import mysql.connector
from datetime import datetime, date
import hashlib

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.class_model import Class
from app.models.student import Student
from app.models.schedule import Schedule
from app.models.attendance import Attendance
from app.models.event import Event
from app.models.finance import Finance
from app.models.expense import Expense, ExpenseCategory

class FlaskToPhpMigrator:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',  # Update with your MySQL password
            'database': 'vietnamese_classroom',
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True
        }
        self.mysql_conn = None
        
    def connect_mysql(self):
        """Connect to MySQL database"""
        try:
            self.mysql_conn = mysql.connector.connect(**self.mysql_config)
            print("✅ Connected to MySQL database")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL connection failed: {e}")
            return False
    
    def close_mysql(self):
        """Close MySQL connection"""
        if self.mysql_conn:
            self.mysql_conn.close()
            print("✅ MySQL connection closed")
    
    def clear_mysql_tables(self):
        """Clear all MySQL tables"""
        try:
            cursor = self.mysql_conn.cursor()
            
            # Disable foreign key checks
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            
            tables = [
                'attendance', 'student_schedules', 'schedules', 'students', 
                'classes', 'events', 'expenses', 'expense_categories', 
                'finances', 'donation_assets', 'users'
            ]
            
            for table in tables:
                cursor.execute(f"TRUNCATE TABLE {table}")
                print(f"   Cleared table: {table}")
            
            # Re-enable foreign key checks
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            cursor.close()
            
            print("✅ All MySQL tables cleared")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ Failed to clear tables: {e}")
            return False
    
    def migrate_users(self):
        """Migrate users from Flask to PHP/MySQL"""
        print("👥 Migrating users...")
        
        try:
            # Get Flask users
            flask_users = User.query.all()
            
            cursor = self.mysql_conn.cursor()
            
            user_mapping = {}  # Flask ID -> MySQL ID
            
            for flask_user in flask_users:
                # Insert into MySQL
                sql = """
                INSERT INTO users (username, email, password_hash, full_name, phone, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    flask_user.username,
                    flask_user.email,
                    flask_user.password_hash,  # Keep existing hash
                    flask_user.full_name,
                    flask_user.phone,
                    flask_user.role,
                    flask_user.is_active,
                    flask_user.created_at or datetime.now()
                )
                
                cursor.execute(sql, values)
                mysql_id = cursor.lastrowid
                user_mapping[flask_user.id] = mysql_id
                
                print(f"   ✅ Migrated user: {flask_user.username} ({flask_user.role})")
            
            cursor.close()
            print(f"   📊 Total users migrated: {len(user_mapping)}")
            return user_mapping
            
        except Exception as e:
            print(f"   ❌ User migration failed: {e}")
            return {}
    
    def migrate_classes(self, user_mapping):
        """Migrate classes from Flask to PHP/MySQL"""
        print("🏫 Migrating classes...")
        
        try:
            flask_classes = Class.query.all()
            
            cursor = self.mysql_conn.cursor()
            class_mapping = {}  # Flask ID -> MySQL ID
            
            for flask_class in flask_classes:
                # Map manager ID
                manager_id = None
                if flask_class.manager_id and flask_class.manager_id in user_mapping:
                    manager_id = user_mapping[flask_class.manager_id]
                
                sql = """
                INSERT INTO classes (name, description, manager_id, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s)
                """
                values = (
                    flask_class.name,
                    flask_class.description,
                    manager_id,
                    flask_class.is_active,
                    flask_class.created_at or datetime.now()
                )
                
                cursor.execute(sql, values)
                mysql_id = cursor.lastrowid
                class_mapping[flask_class.id] = mysql_id
                
                print(f"   ✅ Migrated class: {flask_class.name}")
            
            cursor.close()
            print(f"   📊 Total classes migrated: {len(class_mapping)}")
            return class_mapping
            
        except Exception as e:
            print(f"   ❌ Class migration failed: {e}")
            return {}
    
    def migrate_students(self, class_mapping):
        """Migrate students from Flask to PHP/MySQL"""
        print("🎓 Migrating students...")
        
        try:
            flask_students = Student.query.all()
            
            cursor = self.mysql_conn.cursor()
            student_mapping = {}  # Flask ID -> MySQL ID
            
            for flask_student in flask_students:
                # Map class ID
                class_id = None
                if flask_student.class_id and flask_student.class_id in class_mapping:
                    class_id = class_mapping[flask_student.class_id]
                
                sql = """
                INSERT INTO students (student_code, full_name, date_of_birth, address, 
                                    parent_name, parent_phone, profile_url, class_id, 
                                    is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    flask_student.student_code,
                    flask_student.full_name,
                    flask_student.date_of_birth,
                    flask_student.address,
                    flask_student.parent_name,
                    flask_student.parent_phone,
                    flask_student.profile_url,
                    class_id,
                    flask_student.is_active,
                    flask_student.created_at or datetime.now()
                )
                
                cursor.execute(sql, values)
                mysql_id = cursor.lastrowid
                student_mapping[flask_student.id] = mysql_id
                
                print(f"   ✅ Migrated student: {flask_student.student_code} - {flask_student.full_name}")
            
            cursor.close()
            print(f"   📊 Total students migrated: {len(student_mapping)}")
            return student_mapping
            
        except Exception as e:
            print(f"   ❌ Student migration failed: {e}")
            return {}
    
    def migrate_schedules(self, class_mapping, user_mapping):
        """Migrate schedules from Flask to PHP/MySQL"""
        print("📅 Migrating schedules...")
        
        try:
            flask_schedules = Schedule.query.all()
            
            cursor = self.mysql_conn.cursor()
            schedule_mapping = {}  # Flask ID -> MySQL ID
            
            for flask_schedule in flask_schedules:
                # Map class and teacher IDs
                class_id = None
                if flask_schedule.class_id and flask_schedule.class_id in class_mapping:
                    class_id = class_mapping[flask_schedule.class_id]
                
                teacher_id = None
                if flask_schedule.teacher_id and flask_schedule.teacher_id in user_mapping:
                    teacher_id = user_mapping[flask_schedule.teacher_id]
                
                if not class_id or not teacher_id:
                    print(f"   ⚠️  Skipping schedule - missing class or teacher reference")
                    continue
                
                sql = """
                INSERT INTO schedules (class_id, teacher_id, day_of_week, session, 
                                     start_time, end_time, subject, room, week_number, 
                                     week_created, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    class_id,
                    teacher_id,
                    flask_schedule.day_of_week,
                    flask_schedule.session,
                    flask_schedule.start_time,
                    flask_schedule.end_time,
                    flask_schedule.subject,
                    flask_schedule.room,
                    flask_schedule.week_number or self.get_current_week(),
                    flask_schedule.week_created or self.get_current_week(),
                    flask_schedule.is_active,
                    flask_schedule.created_at or datetime.now()
                )
                
                cursor.execute(sql, values)
                mysql_id = cursor.lastrowid
                schedule_mapping[flask_schedule.id] = mysql_id
                
                print(f"   ✅ Migrated schedule: Class {class_id} - Teacher {teacher_id} - Week {flask_schedule.week_number}")
            
            cursor.close()
            print(f"   📊 Total schedules migrated: {len(schedule_mapping)}")
            return schedule_mapping
            
        except Exception as e:
            print(f"   ❌ Schedule migration failed: {e}")
            return {}
    
    def migrate_attendance(self, student_mapping, schedule_mapping):
        """Migrate attendance from Flask to PHP/MySQL"""
        print("✅ Migrating attendance...")
        
        try:
            flask_attendance = Attendance.query.all()
            
            cursor = self.mysql_conn.cursor()
            migrated_count = 0
            
            for flask_att in flask_attendance:
                # Map student and schedule IDs
                student_id = None
                if flask_att.student_id and flask_att.student_id in student_mapping:
                    student_id = student_mapping[flask_att.student_id]
                
                schedule_id = None
                if flask_att.schedule_id and flask_att.schedule_id in schedule_mapping:
                    schedule_id = schedule_mapping[flask_att.schedule_id]
                
                if not student_id or not schedule_id:
                    continue
                
                sql = """
                INSERT INTO attendance (student_id, schedule_id, date, status, notes, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                values = (
                    student_id,
                    schedule_id,
                    flask_att.date,
                    flask_att.status,
                    flask_att.notes,
                    flask_att.created_at or datetime.now()
                )
                
                cursor.execute(sql, values)
                migrated_count += 1
                
                if migrated_count % 50 == 0:
                    print(f"   📊 Migrated {migrated_count} attendance records...")
            
            cursor.close()
            print(f"   📊 Total attendance records migrated: {migrated_count}")
            return True
            
        except Exception as e:
            print(f"   ❌ Attendance migration failed: {e}")
            return False
    
    def get_current_week(self):
        """Get current week in YYYY-WXX format"""
        now = datetime.now()
        year, week, _ = now.isocalendar()
        return f"{year}-W{week:02d}"
    
    def run_migration(self):
        """Run complete migration"""
        print("🚀 Starting Flask to PHP Migration...")
        print("=" * 60)
        
        # Connect to MySQL
        if not self.connect_mysql():
            return False
        
        # Clear existing data
        print("\n🧹 Clearing existing MySQL data...")
        if not self.clear_mysql_tables():
            return False
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            try:
                print("\n" + "=" * 60)
                
                # Run migrations in order
                user_mapping = self.migrate_users()
                if not user_mapping:
                    print("❌ User migration failed, stopping")
                    return False
                
                class_mapping = self.migrate_classes(user_mapping)
                student_mapping = self.migrate_students(class_mapping)
                schedule_mapping = self.migrate_schedules(class_mapping, user_mapping)
                self.migrate_attendance(student_mapping, schedule_mapping)
                
                print("\n" + "=" * 60)
                print("🎉 Migration completed successfully!")
                
                # Summary
                print("\n📊 Migration Summary:")
                print(f"   Users: {len(user_mapping)}")
                print(f"   Classes: {len(class_mapping)}")
                print(f"   Students: {len(student_mapping)}")
                print(f"   Schedules: {len(schedule_mapping)}")
                
                print("\n🔑 Default Login Credentials (PHP):")
                print("   Username: admin")
                print("   Password: admin123")
                print("   URL: http://localhost/php_version/auth/login.php")
                
                return True
                
            except Exception as e:
                print(f"\n❌ Migration failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            finally:
                self.close_mysql()

def main():
    print("🔄 Vietnamese Classroom Management - Flask to PHP Migration")
    print("=" * 60)
    
    # Confirm before proceeding
    response = input("⚠️  This will clear all existing MySQL data. Continue? (y/N): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        sys.exit(0)
    
    migrator = FlaskToPhpMigrator()
    success = migrator.run_migration()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("\n💡 Next steps:")
        print("   1. Configure web server (Apache/Nginx)")
        print("   2. Set up PHP environment")
        print("   3. Test the PHP application")
        print("   4. Complete remaining features")
    else:
        print("\n❌ Migration failed. Please check the errors above.")
        sys.exit(1)

if __name__ == '__main__':
    main()
