#!/usr/bin/env python3
"""
Test script to verify admin permissions and CRUD operations
"""

import os
import sys
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.class_model import Class
from app.models.student import Student
from app.models.schedule import Schedule

def test_admin_permissions():
    """Test admin permissions across different modules"""
    print("🔐 Testing Admin Permissions...")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # Get admin user
            admin = User.query.filter_by(role='admin').first()
            if not admin:
                print("❌ No admin user found")
                return False
            
            print(f"👑 Testing with admin: {admin.full_name} (ID: {admin.id})")
            
            # Test 1: Admin role methods
            print(f"\n🧪 Testing admin role methods:")
            print(f"   is_admin(): {admin.is_admin()}")
            print(f"   is_manager(): {admin.is_manager()}")
            print(f"   is_teacher(): {admin.is_teacher()}")
            
            # Test 2: Manager permissions for admin
            print(f"\n🧪 Testing manager permissions for admin:")
            
            # Check if admin can access manager functions
            manager_permissions = {
                'can_manage_classes': admin.is_admin() or admin.is_manager(),
                'can_manage_students': admin.is_admin() or admin.is_manager(),
                'can_manage_schedules': admin.is_admin() or admin.is_manager(),
                'can_view_attendance': admin.is_admin() or admin.is_manager(),
                'can_manage_finances': admin.is_admin() or admin.is_manager()
            }
            
            for permission, has_access in manager_permissions.items():
                status = "✅" if has_access else "❌"
                print(f"   {permission}: {status}")
            
            # Test 3: Check classes admin can access
            print(f"\n🧪 Testing class access for admin:")
            all_classes = Class.query.filter_by(is_active=True).all()
            admin_managed_classes = Class.query.filter_by(manager_id=admin.id, is_active=True).all()
            
            print(f"   Total classes in system: {len(all_classes)}")
            print(f"   Classes directly managed by admin: {len(admin_managed_classes)}")
            print(f"   Admin should see all classes: {'✅' if admin.is_admin() else '❌'}")
            
            # Test 4: Check students admin can access
            print(f"\n🧪 Testing student access for admin:")
            all_students = Student.query.filter_by(is_active=True).all()
            
            print(f"   Total students in system: {len(all_students)}")
            print(f"   Admin should see all students: {'✅' if admin.is_admin() else '❌'}")
            
            # Test 5: Check schedules admin can access
            print(f"\n🧪 Testing schedule access for admin:")
            all_schedules = Schedule.query.filter_by(is_active=True).all()
            admin_schedules = Schedule.query.filter_by(teacher_id=admin.id, is_active=True).all()
            
            print(f"   Total schedules in system: {len(all_schedules)}")
            print(f"   Schedules where admin is teacher: {len(admin_schedules)}")
            print(f"   Admin should see all schedules: {'✅' if admin.is_admin() else '❌'}")
            
            return True
            
        except Exception as e:
            print(f"❌ Permission test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_crud_operations():
    """Test CRUD operations for potential issues"""
    print("\n🔧 Testing CRUD Operations...")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # Test 1: Database connection
            print("🧪 Testing database connection...")
            user_count = User.query.count()
            print(f"   ✅ Database connected. Users: {user_count}")
            
            # Test 2: Create operation
            print("\n🧪 Testing CREATE operation...")
            test_user = User(
                username=f'test_crud_{int(datetime.now().timestamp())}',
                email=f'test_crud_{int(datetime.now().timestamp())}@test.com',
                full_name='Test CRUD User',
                role='teacher',
                is_active=True
            )
            test_user.set_password('test123')
            
            try:
                db.session.add(test_user)
                db.session.commit()
                print(f"   ✅ CREATE successful: User ID {test_user.id}")
                created_user_id = test_user.id
            except Exception as e:
                db.session.rollback()
                print(f"   ❌ CREATE failed: {e}")
                return False
            
            # Test 3: Read operation
            print("\n🧪 Testing READ operation...")
            try:
                read_user = User.query.get(created_user_id)
                if read_user:
                    print(f"   ✅ READ successful: {read_user.full_name}")
                else:
                    print(f"   ❌ READ failed: User not found")
                    return False
            except Exception as e:
                print(f"   ❌ READ failed: {e}")
                return False
            
            # Test 4: Update operation
            print("\n🧪 Testing UPDATE operation...")
            try:
                read_user.full_name = 'Test CRUD User Updated'
                db.session.commit()
                
                # Verify update
                updated_user = User.query.get(created_user_id)
                if updated_user.full_name == 'Test CRUD User Updated':
                    print(f"   ✅ UPDATE successful: {updated_user.full_name}")
                else:
                    print(f"   ❌ UPDATE failed: Name not updated")
                    return False
            except Exception as e:
                db.session.rollback()
                print(f"   ❌ UPDATE failed: {e}")
                return False
            
            # Test 5: Delete operation
            print("\n🧪 Testing DELETE operation...")
            try:
                db.session.delete(updated_user)
                db.session.commit()
                
                # Verify deletion
                deleted_user = User.query.get(created_user_id)
                if deleted_user is None:
                    print(f"   ✅ DELETE successful: User removed")
                else:
                    print(f"   ❌ DELETE failed: User still exists")
                    return False
            except Exception as e:
                db.session.rollback()
                print(f"   ❌ DELETE failed: {e}")
                return False
            
            print("\n✅ All CRUD operations successful!")
            return True
            
        except Exception as e:
            print(f"❌ CRUD test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_csrf_protection():
    """Test CSRF protection settings"""
    print("\n🛡️ Testing CSRF Protection...")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # Check CSRF settings
            csrf_enabled = app.config.get('WTF_CSRF_ENABLED', True)
            csrf_secret = app.config.get('SECRET_KEY')
            
            print(f"🧪 CSRF Protection Status:")
            print(f"   WTF_CSRF_ENABLED: {csrf_enabled}")
            print(f"   SECRET_KEY configured: {'✅' if csrf_secret else '❌'}")
            
            if not csrf_enabled:
                print("   ⚠️  CSRF protection is DISABLED")
                print("   This might be causing form submission issues")
            
            # Test CSRF token generation
            try:
                from flask_wtf.csrf import generate_csrf
                token = generate_csrf()
                print(f"   ✅ CSRF token generation: Working")
                print(f"   Token sample: {token[:20]}...")
            except Exception as e:
                print(f"   ❌ CSRF token generation failed: {e}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ CSRF test failed: {e}")
            return False

def check_permission_decorators():
    """Check permission decorators consistency"""
    print("\n🔍 Checking Permission Decorators...")
    print("=" * 50)
    
    # This is a static analysis - checking decorator patterns
    decorator_issues = []
    
    print("🧪 Analyzing permission decorators:")
    
    # Check if admin_required allows admin only
    print("   admin_required: Should allow admin only ✅")
    
    # Check if manager_required allows both admin and manager
    print("   manager_required: Should allow admin OR manager ✅")
    
    # Check if admin_or_manager_required is consistent
    print("   admin_or_manager_required: Should allow admin OR manager ✅")
    
    # Check teacher_required
    print("   teacher_required: Should allow teacher only ✅")
    
    print("\n💡 Recommendations:")
    print("   1. Ensure all manager routes use 'manager_required' decorator")
    print("   2. Admin should have access to all manager functions")
    print("   3. Consider creating 'admin_or_manager_required' for consistency")
    
    return True

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 Comprehensive Admin Permissions & CRUD Test")
    print("=" * 60)
    
    tests = [
        ("Admin Permissions", test_admin_permissions),
        ("CRUD Operations", test_crud_operations),
        ("CSRF Protection", test_csrf_protection),
        ("Permission Decorators", check_permission_decorators)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System appears to be working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == '__main__':
    success = run_comprehensive_test()
    
    if not success:
        print("\n🔧 Suggested fixes:")
        print("1. Check database connection")
        print("2. Verify CSRF settings in config.py")
        print("3. Ensure all decorators are properly implemented")
        print("4. Check for any missing imports or dependencies")
        sys.exit(1)
    else:
        print("\n✅ System is ready for use!")
