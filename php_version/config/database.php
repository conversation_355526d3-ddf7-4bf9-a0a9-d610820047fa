<?php
/**
 * Database Configuration
 * Vietnamese Classroom Management System
 */

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $charset;
    public $conn;

    public function __construct() {
        // Load configuration from environment or use defaults
        $this->host = $_ENV['DB_HOST'] ?? 'localhost';
        $this->db_name = $_ENV['DB_NAME'] ?? 'vietnamese_classroom';
        $this->username = $_ENV['DB_USER'] ?? 'root';
        $this->password = $_ENV['DB_PASS'] ?? '';
        $this->charset = 'utf8mb4';
    }

    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed");
        }

        return $this->conn;
    }

    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get database statistics
     */
    public function getStats() {
        try {
            $conn = $this->getConnection();
            
            $stats = [];
            
            // Count users
            $stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
            $stats['users'] = $stmt->fetch()['count'];
            
            // Count classes
            $stmt = $conn->query("SELECT COUNT(*) as count FROM classes WHERE is_active = 1");
            $stats['classes'] = $stmt->fetch()['count'];
            
            // Count students
            $stmt = $conn->query("SELECT COUNT(*) as count FROM students WHERE is_active = 1");
            $stats['students'] = $stmt->fetch()['count'];
            
            // Count schedules
            $stmt = $conn->query("SELECT COUNT(*) as count FROM schedules WHERE is_active = 1");
            $stats['schedules'] = $stmt->fetch()['count'];
            
            // Count attendance records
            $stmt = $conn->query("SELECT COUNT(*) as count FROM attendance");
            $stats['attendance'] = $stmt->fetch()['count'];
            
            return $stats;
        } catch (Exception $e) {
            error_log("Stats error: " . $e->getMessage());
            return [
                'users' => 0,
                'classes' => 0,
                'students' => 0,
                'schedules' => 0,
                'attendance' => 0
            ];
        }
    }
}

/**
 * Database Configuration Constants
 */
define('DB_CONFIG', [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'name' => $_ENV['DB_NAME'] ?? 'vietnamese_classroom',
    'user' => $_ENV['DB_USER'] ?? 'root',
    'pass' => $_ENV['DB_PASS'] ?? '',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]
]);

/**
 * Quick database connection function
 */
function getDB() {
    static $db = null;
    if ($db === null) {
        $db = new Database();
    }
    return $db->getConnection();
}

/**
 * Execute query with parameters
 */
function executeQuery($sql, $params = []) {
    try {
        $conn = getDB();
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query error: " . $e->getMessage());
        throw new Exception("Database query failed");
    }
}

/**
 * Fetch single row
 */
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Fetch all rows
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert and return last insert ID
 */
function insertAndGetId($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return getDB()->lastInsertId();
}

/**
 * Get current week in YYYY-WXX format
 */
function getCurrentWeek() {
    $year = date('Y');
    $week = date('W');
    return sprintf('%d-W%02d', $year, $week);
}

/**
 * Get next week in YYYY-WXX format
 */
function getNextWeek() {
    $nextWeek = strtotime('+1 week');
    $year = date('Y', $nextWeek);
    $week = date('W', $nextWeek);
    return sprintf('%d-W%02d', $year, $week);
}

/**
 * Get previous week in YYYY-WXX format
 */
function getPreviousWeek() {
    $prevWeek = strtotime('-1 week');
    $year = date('Y', $prevWeek);
    $week = date('W', $prevWeek);
    return sprintf('%d-W%02d', $year, $week);
}

/**
 * Parse week string to get year and week number
 */
function parseWeek($weekStr) {
    if (preg_match('/^(\d{4})-W(\d{2})$/', $weekStr, $matches)) {
        return [
            'year' => (int)$matches[1],
            'week' => (int)$matches[2]
        ];
    }
    return false;
}

/**
 * Get week start and end dates
 */
function getWeekDates($weekStr) {
    $parsed = parseWeek($weekStr);
    if (!$parsed) {
        return false;
    }
    
    $year = $parsed['year'];
    $week = $parsed['week'];
    
    // Get first day of the year
    $jan1 = new DateTime("$year-01-01");
    
    // Calculate week start (Monday)
    $weekStart = clone $jan1;
    $weekStart->setISODate($year, $week, 1);
    
    // Calculate week end (Sunday)
    $weekEnd = clone $weekStart;
    $weekEnd->modify('+6 days');
    
    return [
        'start' => $weekStart,
        'end' => $weekEnd
    ];
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate secure password hash
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Log activity
 */
function logActivity($user_id, $action, $details = '') {
    try {
        $sql = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())";
        $params = [
            $user_id,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        executeQuery($sql, $params);
    } catch (Exception $e) {
        error_log("Activity log error: " . $e->getMessage());
    }
}
?>
