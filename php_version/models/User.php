<?php
/**
 * User Model
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class User {
    private $db;
    private $table = 'users';
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all users with pagination
     */
    public function getAll($page = 1, $limit = 20, $search = '') {
        try {
            $conn = $this->db->getConnection();
            $offset = ($page - 1) * $limit;
            
            $whereClause = "WHERE 1=1";
            $params = [];
            
            if (!empty($search)) {
                $whereClause .= " AND (full_name LIKE ? OR username LIKE ? OR email LIKE ?)";
                $searchTerm = "%$search%";
                $params = [$searchTerm, $searchTerm, $searchTerm];
            }
            
            // Get total count
            $countSql = "SELECT COUNT(*) as total FROM {$this->table} $whereClause";
            $countStmt = $conn->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetch()['total'];
            
            // Get users
            $sql = "SELECT id, username, email, full_name, phone, role, is_active, created_at 
                    FROM {$this->table} $whereClause 
                    ORDER BY created_at DESC 
                    LIMIT $limit OFFSET $offset";
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            $users = $stmt->fetchAll();
            
            return [
                'success' => true,
                'data' => $users,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($total / $limit),
                    'total_records' => $total,
                    'limit' => $limit
                ]
            ];
        } catch (Exception $e) {
            error_log("Get users error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi lấy danh sách người dùng'];
        }
    }
    
    /**
     * Get user by ID
     */
    public function getById($id) {
        try {
            $conn = $this->db->getConnection();
            $sql = "SELECT * FROM {$this->table} WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$id]);
            $user = $stmt->fetch();
            
            if ($user) {
                return ['success' => true, 'data' => $user];
            } else {
                return ['success' => false, 'message' => 'Không tìm thấy người dùng'];
            }
        } catch (Exception $e) {
            error_log("Get user by ID error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi lấy thông tin người dùng'];
        }
    }
    
    /**
     * Get user by username
     */
    public function getByUsername($username) {
        try {
            $conn = $this->db->getConnection();
            $sql = "SELECT * FROM {$this->table} WHERE username = ? AND is_active = 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user) {
                return ['success' => true, 'data' => $user];
            } else {
                return ['success' => false, 'message' => 'Không tìm thấy người dùng'];
            }
        } catch (Exception $e) {
            error_log("Get user by username error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi lấy thông tin người dùng'];
        }
    }
    
    /**
     * Create new user
     */
    public function create($data) {
        try {
            $conn = $this->db->getConnection();
            
            // Validate required fields
            $required = ['username', 'email', 'password', 'full_name', 'role'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return ['success' => false, 'message' => "Trường {$field} là bắt buộc"];
                }
            }
            
            // Check if username or email exists
            $checkSql = "SELECT id FROM {$this->table} WHERE username = ? OR email = ?";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->execute([$data['username'], $data['email']]);
            
            if ($checkStmt->fetch()) {
                return ['success' => false, 'message' => 'Tên đăng nhập hoặc email đã tồn tại'];
            }
            
            // Hash password
            $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Insert user
            $sql = "INSERT INTO {$this->table} (username, email, password_hash, full_name, phone, role, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $params = [
                $data['username'],
                $data['email'],
                $passwordHash,
                $data['full_name'],
                $data['phone'] ?? null,
                $data['role'],
                $data['is_active'] ?? 1
            ];
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            
            $userId = $conn->lastInsertId();
            
            return [
                'success' => true,
                'data' => ['id' => $userId],
                'message' => 'Tạo người dùng thành công'
            ];
            
        } catch (Exception $e) {
            error_log("Create user error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi tạo người dùng'];
        }
    }
    
    /**
     * Update user
     */
    public function update($id, $data) {
        try {
            $conn = $this->db->getConnection();
            
            // Check if user exists
            $user = $this->getById($id);
            if (!$user['success']) {
                return $user;
            }
            
            // Build update query
            $updateFields = [];
            $params = [];
            
            $allowedFields = ['username', 'email', 'full_name', 'phone', 'role', 'is_active'];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return ['success' => false, 'message' => 'Không có dữ liệu để cập nhật'];
            }
            
            // Check for duplicate username/email (excluding current user)
            if (isset($data['username']) || isset($data['email'])) {
                $checkSql = "SELECT id FROM {$this->table} WHERE (username = ? OR email = ?) AND id != ?";
                $checkParams = [
                    $data['username'] ?? $user['data']['username'],
                    $data['email'] ?? $user['data']['email'],
                    $id
                ];
                $checkStmt = $conn->prepare($checkSql);
                $checkStmt->execute($checkParams);
                
                if ($checkStmt->fetch()) {
                    return ['success' => false, 'message' => 'Tên đăng nhập hoặc email đã tồn tại'];
                }
            }
            
            // Update password if provided
            if (!empty($data['password'])) {
                $updateFields[] = "password_hash = ?";
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            $updateFields[] = "updated_at = NOW()";
            $params[] = $id;
            
            $sql = "UPDATE {$this->table} SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            
            return [
                'success' => true,
                'message' => 'Cập nhật người dùng thành công'
            ];
            
        } catch (Exception $e) {
            error_log("Update user error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi cập nhật người dùng'];
        }
    }
    
    /**
     * Delete user
     */
    public function delete($id) {
        try {
            $conn = $this->db->getConnection();
            
            // Check if user exists
            $user = $this->getById($id);
            if (!$user['success']) {
                return $user;
            }
            
            // Soft delete (set is_active = 0)
            $sql = "UPDATE {$this->table} SET is_active = 0, updated_at = NOW() WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$id]);
            
            return [
                'success' => true,
                'message' => 'Xóa người dùng thành công'
            ];
            
        } catch (Exception $e) {
            error_log("Delete user error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi xóa người dùng'];
        }
    }
    
    /**
     * Get users by role
     */
    public function getByRole($role) {
        try {
            $conn = $this->db->getConnection();
            $sql = "SELECT id, username, full_name, email, phone FROM {$this->table} 
                    WHERE role = ? AND is_active = 1 
                    ORDER BY full_name";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$role]);
            $users = $stmt->fetchAll();
            
            return ['success' => true, 'data' => $users];
        } catch (Exception $e) {
            error_log("Get users by role error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi lấy danh sách người dùng'];
        }
    }
    
    /**
     * Get managers
     */
    public function getManagers() {
        return $this->getByRole('manager');
    }
    
    /**
     * Get teachers
     */
    public function getTeachers() {
        return $this->getByRole('teacher');
    }
    
    /**
     * Get user statistics
     */
    public function getStats() {
        try {
            $conn = $this->db->getConnection();
            
            $stats = [];
            
            // Total users
            $stmt = $conn->query("SELECT COUNT(*) as count FROM {$this->table} WHERE is_active = 1");
            $stats['total'] = $stmt->fetch()['count'];
            
            // Users by role
            $stmt = $conn->query("SELECT role, COUNT(*) as count FROM {$this->table} WHERE is_active = 1 GROUP BY role");
            $roleStats = $stmt->fetchAll();
            
            foreach ($roleStats as $roleStat) {
                $stats[$roleStat['role']] = $roleStat['count'];
            }
            
            return ['success' => true, 'data' => $stats];
        } catch (Exception $e) {
            error_log("Get user stats error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi lấy thống kê'];
        }
    }
    
    /**
     * Validate user data
     */
    public function validate($data, $isUpdate = false) {
        $errors = [];
        
        // Username validation
        if (!$isUpdate || isset($data['username'])) {
            if (empty($data['username'])) {
                $errors[] = 'Tên đăng nhập là bắt buộc';
            } elseif (strlen($data['username']) < 3) {
                $errors[] = 'Tên đăng nhập phải có ít nhất 3 ký tự';
            } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                $errors[] = 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới';
            }
        }
        
        // Email validation
        if (!$isUpdate || isset($data['email'])) {
            if (empty($data['email'])) {
                $errors[] = 'Email là bắt buộc';
            } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Email không hợp lệ';
            }
        }
        
        // Password validation (only for create or when password is provided)
        if (!$isUpdate || !empty($data['password'])) {
            if (empty($data['password'])) {
                $errors[] = 'Mật khẩu là bắt buộc';
            } elseif (strlen($data['password']) < 6) {
                $errors[] = 'Mật khẩu phải có ít nhất 6 ký tự';
            }
        }
        
        // Full name validation
        if (!$isUpdate || isset($data['full_name'])) {
            if (empty($data['full_name'])) {
                $errors[] = 'Họ tên là bắt buộc';
            } elseif (strlen($data['full_name']) < 2) {
                $errors[] = 'Họ tên phải có ít nhất 2 ký tự';
            }
        }
        
        // Role validation
        if (!$isUpdate || isset($data['role'])) {
            $validRoles = ['admin', 'manager', 'teacher', 'user'];
            if (empty($data['role'])) {
                $errors[] = 'Vai trò là bắt buộc';
            } elseif (!in_array($data['role'], $validRoles)) {
                $errors[] = 'Vai trò không hợp lệ';
            }
        }
        
        // Phone validation (optional)
        if (isset($data['phone']) && !empty($data['phone'])) {
            if (!preg_match('/^[0-9+\-\s()]+$/', $data['phone'])) {
                $errors[] = 'Số điện thoại không hợp lệ';
            }
        }
        
        return $errors;
    }
}
?>
