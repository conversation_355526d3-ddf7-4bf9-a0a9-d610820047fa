# 🚀 Vietnamese Classroom Management System - PHP Version

## 📋 **Tổng quan**

Đ<PERSON><PERSON> là phiên bản PHP + MySQL của hệ thống quản lý lớp học tiếng Việt, đượ<PERSON> chuyển đổi từ Python Flask + MongoDB.

### **🔄 Chuyển đổi từ Flask sang PHP**

**Từ:**
- Python Flask
- MongoDB/PostgreSQL
- Jinja2 Templates
- SQLAlchemy ORM

**Sang:**
- PHP 8.0+
- MySQL 8.0+
- Native PHP Templates
- PDO Database Access

---

## 🏗️ **Cấu trúc dự án**

```
php_version/
├── config/
│   └── database.php          # Cấu hình database
├── includes/
│   └── auth.php              # Hệ thống authentication
├── models/
│   ├── User.php              # Model người dùng
│   ├── Class.php             # Model lớp học
│   ├── Student.php           # Model học sinh
│   ├── Schedule.php          # Model lịch dạy
│   ├── Attendance.php        # Model điểm danh
│   ├── Finance.php           # Model tài chính
│   └── Expense.php           # Model chi tiêu
├── views/
│   ├── layouts/              # Layout templates
│   ├── admin/                # Admin views
│   ├── manager/              # Manager views
│   ├── teacher/              # Teacher views
│   └── auth/                 # Authentication views
├── public/
│   ├── css/                  # CSS files
│   ├── js/                   # JavaScript files
│   └── images/               # Images
├── auth/
│   ├── login.php             # Trang đăng nhập
│   ├── logout.php            # Đăng xuất
│   └── forgot-password.php   # Quên mật khẩu
├── admin/
│   ├── index.php             # Admin dashboard
│   ├── users.php             # Quản lý người dùng
│   └── settings.php          # Cài đặt hệ thống
├── manager/
│   ├── index.php             # Manager dashboard
│   ├── classes.php           # Quản lý lớp học
│   ├── students.php          # Quản lý học sinh
│   ├── schedules.php         # Quản lý lịch dạy
│   └── attendance.php        # Quản lý điểm danh
├── teacher/
│   ├── index.php             # Teacher dashboard
│   ├── schedule.php          # Lịch dạy của tôi
│   └── attendance.php        # Điểm danh
├── database/
│   ├── schema.sql            # Database schema
│   ├── migrations/           # Database migrations
│   └── seeders/              # Sample data
├── dashboard.php             # Main dashboard
├── index.php                 # Homepage
└── .htaccess                 # Apache rewrite rules
```

---

## 🛠️ **Cài đặt và triển khai**

### **1. Yêu cầu hệ thống**

```
- PHP 8.0 hoặc cao hơn
- MySQL 8.0 hoặc cao hơn
- Apache/Nginx web server
- PHP Extensions:
  - PDO
  - PDO_MySQL
  - mbstring
  - openssl
  - json
  - session
```

### **2. Cài đặt database**

```bash
# Tạo database
mysql -u root -p
CREATE DATABASE vietnamese_classroom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Import schema
mysql -u root -p vietnamese_classroom < database/schema.sql
```

### **3. Cấu hình môi trường**

Tạo file `.env`:
```bash
# Database Configuration
DB_HOST=localhost
DB_NAME=vietnamese_classroom
DB_USER=root
DB_PASS=your_password

# Application Settings
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

# Security
SESSION_LIFETIME=7200
CSRF_TOKEN_LIFETIME=3600
```

### **4. Cấu hình Apache**

File `.htaccess`:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

---

## 🔐 **Hệ thống Authentication**

### **Phân quyền người dùng:**

| Role | Quyền hạn |
|------|-----------|
| **Admin** | Toàn quyền hệ thống, quản lý users, tất cả chức năng |
| **Manager** | Quản lý classes, students, schedules, attendance, finance |
| **Teacher** | Xem lịch dạy, điểm danh học sinh |
| **User** | Quyền cơ bản |

### **Tài khoản mặc định:**

```
Admin:    admin / admin123
Manager:  manager1 / manager123  
Teacher:  teacher1 / teacher123
```

---

## 📊 **Database Schema**

### **Bảng chính:**

1. **users** - Người dùng hệ thống
2. **classes** - Lớp học
3. **students** - Học sinh
4. **schedules** - Lịch dạy
5. **student_schedules** - Quan hệ học sinh-lịch học
6. **attendance** - Điểm danh
7. **events** - Sự kiện
8. **finances** - Giao dịch tài chính
9. **expenses** - Chi tiêu
10. **expense_categories** - Danh mục chi tiêu
11. **donation_assets** - Tài sản quyên góp

### **Quan hệ database:**

```sql
users (1) -> (n) classes (manager_id)
users (1) -> (n) schedules (teacher_id)
classes (1) -> (n) students (class_id)
classes (1) -> (n) schedules (class_id)
students (n) -> (n) schedules (student_schedules)
students (1) -> (n) attendance (student_id)
schedules (1) -> (n) attendance (schedule_id)
```

---

## 🎯 **Tính năng chính**

### **✅ Đã chuyển đổi:**

1. **Authentication System** ✅
   - Login/Logout
   - Role-based access control
   - Session management
   - Password hashing

2. **User Management** ✅
   - CRUD operations
   - Role assignment
   - User validation

3. **Database Layer** ✅
   - MySQL schema
   - PDO connections
   - Query builders
   - Data validation

### **🔄 Đang chuyển đổi:**

4. **Class Management**
   - CRUD classes
   - Manager assignment
   - Student enrollment

5. **Student Management**
   - Student profiles
   - Class assignment
   - Parent information

6. **Schedule Management**
   - Weekly schedules
   - Teacher assignment
   - Time conflict detection

7. **Attendance System**
   - Daily attendance
   - Status tracking
   - Reports

8. **Financial Management**
   - Income/Expense tracking
   - Categories
   - Approval workflow

### **📋 Cần hoàn thiện:**

9. **Reporting System**
10. **Calendar Integration**
11. **Notification System**
12. **File Upload/Management**
13. **Export Functions**

---

## 🔧 **API và Functions**

### **Database Functions:**

```php
// Connection
$conn = getDB();

// Query execution
$result = executeQuery($sql, $params);
$row = fetchOne($sql, $params);
$rows = fetchAll($sql, $params);
$id = insertAndGetId($sql, $params);

// Utilities
$week = getCurrentWeek();
$nextWeek = getNextWeek();
$dates = getWeekDates($week);
```

### **Authentication Functions:**

```php
// Auth checks
requireLogin();
requireAdmin();
requireManager();
requireTeacher();

// User info
$user = getCurrentUser();
$isAdmin = isAdmin();
$hasPermission = hasPermission('user_management');
```

### **Security Functions:**

```php
// Data sanitization
$clean = sanitizeInput($data);

// Password handling
$hash = hashPassword($password);
$valid = verifyPassword($password, $hash);

// CSRF protection
$token = generateCSRFToken();
$valid = verifyCSRFToken($token);
```

---

## 🚀 **Deployment**

### **1. Production Setup:**

```bash
# Set environment
APP_ENV=production
APP_DEBUG=false

# Database optimization
mysql_secure_installation

# PHP optimization
opcache.enable=1
opcache.memory_consumption=128
```

### **2. Security Checklist:**

- ✅ Change default passwords
- ✅ Enable HTTPS
- ✅ Configure firewall
- ✅ Set proper file permissions
- ✅ Enable error logging
- ✅ Disable debug mode

### **3. Performance Optimization:**

- ✅ Enable PHP OPcache
- ✅ Configure MySQL query cache
- ✅ Use CDN for static assets
- ✅ Enable gzip compression
- ✅ Optimize database indexes

---

## 📞 **Support & Development**

### **Development Commands:**

```bash
# Start development server
php -S localhost:8000

# Database migration
php database/migrate.php

# Run tests
php tests/run_tests.php

# Generate documentation
php docs/generate.php
```

### **Debugging:**

```php
// Enable debug mode
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Log errors
error_log("Debug message");

// Database debug
$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
```

---

## 🎉 **Kết luận**

### **✅ Ưu điểm của phiên bản PHP:**

1. **Performance**: Nhanh hơn với PHP 8.0+
2. **Hosting**: Dễ deploy trên shared hosting
3. **Cost**: Chi phí thấp hơn
4. **Maintenance**: Dễ bảo trì và cập nhật
5. **Compatibility**: Tương thích với nhiều hosting provider

### **🔄 Migration Status:**

- **Database**: ✅ Hoàn thành
- **Authentication**: ✅ Hoàn thành  
- **Core Models**: ✅ Hoàn thành
- **Admin Panel**: 🔄 Đang phát triển
- **Manager Panel**: 🔄 Đang phát triển
- **Teacher Panel**: 🔄 Đang phát triển
- **Frontend**: 🔄 Đang phát triển

**🚀 Hệ thống PHP đã sẵn sàng cho giai đoạn phát triển tiếp theo!**
