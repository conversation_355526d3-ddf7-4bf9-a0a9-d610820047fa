<?php
/**
 * Attendance Model
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class Attendance {
    private $conn;
    private $table = 'attendance';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Create new attendance record
     */
    public function create($data) {
        $sql = "INSERT INTO {$this->table} 
                (schedule_id, student_id, date, status, reason, check_in_time, 
                 check_out_time, lesson_content, notes, created_at) 
                VALUES (:schedule_id, :student_id, :date, :status, :reason, :check_in_time,
                        :check_out_time, :lesson_content, :notes, NOW())";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':schedule_id', $data['schedule_id']);
            $stmt->bindParam(':student_id', $data['student_id']);
            $stmt->bindParam(':date', $data['date']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':reason', $data['reason']);
            $stmt->bindParam(':check_in_time', $data['check_in_time']);
            $stmt->bindParam(':check_out_time', $data['check_out_time']);
            $stmt->bindParam(':lesson_content', $data['lesson_content']);
            $stmt->bindParam(':notes', $data['notes']);
            
            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Attendance creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get attendance by ID
     */
    public function getById($id) {
        $sql = "SELECT a.*, s.full_name as student_name, sc.subject, c.name as class_name,
                       u.full_name as teacher_name
                FROM {$this->table} a 
                LEFT JOIN students s ON a.student_id = s.id
                LEFT JOIN schedules sc ON a.schedule_id = sc.id
                LEFT JOIN classes c ON sc.class_id = c.id
                LEFT JOIN users u ON sc.teacher_id = u.id
                WHERE a.id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get attendance error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get attendance by schedule and date
     */
    public function getByScheduleAndDate($schedule_id, $date) {
        $sql = "SELECT a.*, s.full_name as student_name, s.student_code
                FROM {$this->table} a 
                LEFT JOIN students s ON a.student_id = s.id
                WHERE a.schedule_id = :schedule_id AND a.date = :date
                ORDER BY s.full_name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':schedule_id', $schedule_id);
            $stmt->bindParam(':date', $date);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get attendance by schedule and date error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get attendance by student
     */
    public function getByStudent($student_id, $start_date = null, $end_date = null) {
        $sql = "SELECT a.*, sc.subject, c.name as class_name, u.full_name as teacher_name
                FROM {$this->table} a 
                LEFT JOIN schedules sc ON a.schedule_id = sc.id
                LEFT JOIN classes c ON sc.class_id = c.id
                LEFT JOIN users u ON sc.teacher_id = u.id
                WHERE a.student_id = :student_id";
        
        if ($start_date) {
            $sql .= " AND a.date >= :start_date";
        }
        if ($end_date) {
            $sql .= " AND a.date <= :end_date";
        }
        
        $sql .= " ORDER BY a.date DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_id', $student_id);
            if ($start_date) {
                $stmt->bindParam(':start_date', $start_date);
            }
            if ($end_date) {
                $stmt->bindParam(':end_date', $end_date);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get attendance by student error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update attendance
     */
    public function update($id, $data) {
        $sql = "UPDATE {$this->table} 
                SET status = :status, reason = :reason, check_in_time = :check_in_time,
                    check_out_time = :check_out_time, lesson_content = :lesson_content,
                    notes = :notes, updated_at = NOW()
                WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':reason', $data['reason']);
            $stmt->bindParam(':check_in_time', $data['check_in_time']);
            $stmt->bindParam(':check_out_time', $data['check_out_time']);
            $stmt->bindParam(':lesson_content', $data['lesson_content']);
            $stmt->bindParam(':notes', $data['notes']);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Attendance update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete attendance
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Attendance delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Bulk create attendance for all students in a class
     */
    public function bulkCreateForClass($schedule_id, $date, $class_id, $default_status = 'present') {
        // Get all students in the class
        $sql = "SELECT id FROM students WHERE class_id = :class_id AND is_active = 1";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':class_id', $class_id);
            $stmt->execute();
            $students = $stmt->fetchAll();
            
            $success_count = 0;
            foreach ($students as $student) {
                $attendance_data = [
                    'schedule_id' => $schedule_id,
                    'student_id' => $student['id'],
                    'date' => $date,
                    'status' => $default_status,
                    'reason' => null,
                    'check_in_time' => null,
                    'check_out_time' => null,
                    'lesson_content' => null,
                    'notes' => null
                ];
                
                if ($this->create($attendance_data)) {
                    $success_count++;
                }
            }
            
            return $success_count;
        } catch (PDOException $e) {
            error_log("Bulk create attendance error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if attendance exists
     */
    public function exists($schedule_id, $student_id, $date) {
        $sql = "SELECT COUNT(*) FROM {$this->table} 
                WHERE schedule_id = :schedule_id AND student_id = :student_id AND date = :date";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':schedule_id', $schedule_id);
            $stmt->bindParam(':student_id', $student_id);
            $stmt->bindParam(':date', $date);
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Check attendance exists error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get attendance statistics for a student
     */
    public function getStudentStats($student_id, $start_date = null, $end_date = null) {
        $sql = "SELECT 
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN status = 'absent_with_reason' THEN 1 ELSE 0 END) as absent_with_reason_count,
                    SUM(CASE WHEN status = 'absent_without_reason' THEN 1 ELSE 0 END) as absent_without_reason_count
                FROM {$this->table} 
                WHERE student_id = :student_id";
        
        if ($start_date) {
            $sql .= " AND date >= :start_date";
        }
        if ($end_date) {
            $sql .= " AND date <= :end_date";
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_id', $student_id);
            if ($start_date) {
                $stmt->bindParam(':start_date', $start_date);
            }
            if ($end_date) {
                $stmt->bindParam(':end_date', $end_date);
            }
            $stmt->execute();
            $result = $stmt->fetch();
            
            // Calculate attendance rate
            if ($result && $result['total_sessions'] > 0) {
                $result['attendance_rate'] = round(($result['present_count'] / $result['total_sessions']) * 100, 2);
            } else {
                $result['attendance_rate'] = 0;
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Get student attendance stats error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get attendance statistics for a class
     */
    public function getClassStats($class_id, $start_date = null, $end_date = null) {
        $sql = "SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT a.student_id) as total_students,
                    COUNT(DISTINCT a.date) as total_sessions,
                    SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN a.status = 'absent_with_reason' THEN 1 ELSE 0 END) as absent_with_reason_count,
                    SUM(CASE WHEN a.status = 'absent_without_reason' THEN 1 ELSE 0 END) as absent_without_reason_count
                FROM {$this->table} a
                JOIN schedules sc ON a.schedule_id = sc.id
                WHERE sc.class_id = :class_id";
        
        if ($start_date) {
            $sql .= " AND a.date >= :start_date";
        }
        if ($end_date) {
            $sql .= " AND a.date <= :end_date";
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':class_id', $class_id);
            if ($start_date) {
                $stmt->bindParam(':start_date', $start_date);
            }
            if ($end_date) {
                $stmt->bindParam(':end_date', $end_date);
            }
            $stmt->execute();
            $result = $stmt->fetch();
            
            // Calculate attendance rate
            if ($result && $result['total_records'] > 0) {
                $result['attendance_rate'] = round(($result['present_count'] / $result['total_records']) * 100, 2);
            } else {
                $result['attendance_rate'] = 0;
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Get class attendance stats error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get status display name
     */
    public static function getStatusDisplay($status) {
        $status_map = [
            'present' => 'Có mặt',
            'absent_with_reason' => 'Vắng có lý do',
            'absent_without_reason' => 'Vắng không lý do'
        ];
        return $status_map[$status] ?? $status;
    }

    /**
     * Get all attendance records with filters
     */
    public function getAll($filters = []) {
        $sql = "SELECT a.*, s.full_name as student_name, s.student_code,
                       sc.subject, c.name as class_name, u.full_name as teacher_name
                FROM {$this->table} a 
                LEFT JOIN students s ON a.student_id = s.id
                LEFT JOIN schedules sc ON a.schedule_id = sc.id
                LEFT JOIN classes c ON sc.class_id = c.id
                LEFT JOIN users u ON sc.teacher_id = u.id
                WHERE 1=1";
        
        $params = [];
        
        if (!empty($filters['class_id'])) {
            $sql .= " AND sc.class_id = :class_id";
            $params[':class_id'] = $filters['class_id'];
        }
        
        if (!empty($filters['student_id'])) {
            $sql .= " AND a.student_id = :student_id";
            $params[':student_id'] = $filters['student_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND a.date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND a.date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND a.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        $sql .= " ORDER BY a.date DESC, s.full_name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get all attendance error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent attendance by teacher
     */
    public function getRecentByTeacher($teacher_id, $limit = 10) {
        $sql = "SELECT a.*, s.full_name as student_name, sc.subject, c.name as class_name
                FROM {$this->table} a
                LEFT JOIN students s ON a.student_id = s.id
                LEFT JOIN schedules sc ON a.schedule_id = sc.id
                LEFT JOIN classes c ON sc.class_id = c.id
                WHERE sc.teacher_id = :teacher_id
                ORDER BY a.date DESC, a.created_at DESC
                LIMIT :limit";

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':teacher_id', $teacher_id);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get recent attendance by teacher error: " . $e->getMessage());
            return [];
        }
    }
}
?>
