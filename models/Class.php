<?php
/**
 * Class Model
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class ClassModel {
    private $conn;
    private $table = 'classes';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Create new class
     */
    public function create($data) {
        $sql = "INSERT INTO {$this->table} (name, description, manager_id, is_active, created_at) 
                VALUES (:name, :description, :manager_id, :is_active, NOW())";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':manager_id', $data['manager_id']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Class creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get class by ID
     */
    public function getById($id) {
        $sql = "SELECT c.*, u.full_name as manager_name 
                FROM {$this->table} c 
                LEFT JOIN users u ON c.manager_id = u.id 
                WHERE c.id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get class error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all classes
     */
    public function getAll($active_only = true) {
        $sql = "SELECT c.*, u.full_name as manager_name,
                       (SELECT COUNT(*) FROM students s WHERE s.class_id = c.id AND s.is_active = 1) as student_count
                FROM {$this->table} c 
                LEFT JOIN users u ON c.manager_id = u.id";
        
        if ($active_only) {
            $sql .= " WHERE c.is_active = 1";
        }
        
        $sql .= " ORDER BY c.name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get all classes error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update class
     */
    public function update($id, $data) {
        $sql = "UPDATE {$this->table} 
                SET name = :name, description = :description, manager_id = :manager_id, 
                    is_active = :is_active, updated_at = NOW()
                WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':manager_id', $data['manager_id']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Class update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete class (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE {$this->table} SET is_active = 0, updated_at = NOW() WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Class delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get classes by manager
     */
    public function getByManager($manager_id) {
        $sql = "SELECT c.*, 
                       (SELECT COUNT(*) FROM students s WHERE s.class_id = c.id AND s.is_active = 1) as student_count
                FROM {$this->table} c 
                WHERE c.manager_id = :manager_id AND c.is_active = 1
                ORDER BY c.name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':manager_id', $manager_id);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get classes by manager error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get class students
     */
    public function getStudents($class_id) {
        $sql = "SELECT s.* FROM students s 
                WHERE s.class_id = :class_id AND s.is_active = 1
                ORDER BY s.full_name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':class_id', $class_id);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get class students error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get class schedules
     */
    public function getSchedules($class_id, $week_number = null) {
        $sql = "SELECT s.*, u.full_name as teacher_name 
                FROM schedules s 
                LEFT JOIN users u ON s.teacher_id = u.id
                WHERE s.class_id = :class_id AND s.is_active = 1";
        
        if ($week_number) {
            $sql .= " AND s.week_number = :week_number";
        }
        
        $sql .= " ORDER BY s.day_of_week, s.start_time";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':class_id', $class_id);
            if ($week_number) {
                $stmt->bindParam(':week_number', $week_number);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get class schedules error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if class name exists
     */
    public function nameExists($name, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE name = :name AND is_active = 1";
        
        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':name', $name);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Check class name exists error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get class statistics
     */
    public function getStatistics($class_id) {
        $sql = "SELECT 
                    (SELECT COUNT(*) FROM students WHERE class_id = :class_id AND is_active = 1) as total_students,
                    (SELECT COUNT(*) FROM schedules WHERE class_id = :class_id AND is_active = 1) as total_schedules,
                    (SELECT COUNT(DISTINCT week_number) FROM schedules WHERE class_id = :class_id AND is_active = 1) as total_weeks
                ";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':class_id', $class_id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get class statistics error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get total count of classes
     */
    public function getTotalCount($active_only = true) {
        $sql = "SELECT COUNT(*) FROM {$this->table}";

        if ($active_only) {
            $sql .= " WHERE is_active = 1";
        }

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Get total classes count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get classes by manager
     */
    public function getByManager($manager_id) {
        $sql = "SELECT c.*, u.full_name as manager_name,
                       (SELECT COUNT(*) FROM students s WHERE s.class_id = c.id AND s.is_active = 1) as student_count
                FROM {$this->table} c
                LEFT JOIN users u ON c.manager_id = u.id
                WHERE c.manager_id = :manager_id AND c.is_active = 1
                ORDER BY c.name";

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':manager_id', $manager_id);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get classes by manager error: " . $e->getMessage());
            return [];
        }
    }
}
?>
