<?php
/**
 * Finance Model
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class Finance {
    private $conn;
    private $table = 'finances';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Create new finance record
     */
    public function create($data) {
        $sql = "INSERT INTO {$this->table} 
                (title, amount, transaction_type, category, description, transaction_date, 
                 creator_id, is_active, created_at) 
                VALUES (:title, :amount, :transaction_type, :category, :description, 
                        :transaction_date, :creator_id, :is_active, NOW())";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':amount', $data['amount']);
            $stmt->bindParam(':transaction_type', $data['transaction_type']);
            $stmt->bindParam(':category', $data['category']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':transaction_date', $data['transaction_date']);
            $stmt->bindParam(':creator_id', $data['creator_id']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Finance creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get finance record by ID
     */
    public function getById($id) {
        $sql = "SELECT f.*, u.full_name as creator_name 
                FROM {$this->table} f 
                LEFT JOIN users u ON f.creator_id = u.id 
                WHERE f.id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get finance error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all finance records
     */
    public function getAll($filters = []) {
        $sql = "SELECT f.*, u.full_name as creator_name 
                FROM {$this->table} f 
                LEFT JOIN users u ON f.creator_id = u.id 
                WHERE f.is_active = 1";
        
        $params = [];
        
        if (!empty($filters['transaction_type'])) {
            $sql .= " AND f.transaction_type = :transaction_type";
            $params[':transaction_type'] = $filters['transaction_type'];
        }
        
        if (!empty($filters['category'])) {
            $sql .= " AND f.category = :category";
            $params[':category'] = $filters['category'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND f.transaction_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND f.transaction_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['creator_id'])) {
            $sql .= " AND f.creator_id = :creator_id";
            $params[':creator_id'] = $filters['creator_id'];
        }
        
        $sql .= " ORDER BY f.transaction_date DESC, f.created_at DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get all finances error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update finance record
     */
    public function update($id, $data) {
        $sql = "UPDATE {$this->table} 
                SET title = :title, amount = :amount, transaction_type = :transaction_type,
                    category = :category, description = :description, 
                    transaction_date = :transaction_date, is_active = :is_active,
                    updated_at = NOW()
                WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':amount', $data['amount']);
            $stmt->bindParam(':transaction_type', $data['transaction_type']);
            $stmt->bindParam(':category', $data['category']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':transaction_date', $data['transaction_date']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Finance update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete finance record (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE {$this->table} SET is_active = 0, updated_at = NOW() WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Finance delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get financial summary
     */
    public function getSummary($start_date = null, $end_date = null) {
        $sql = "SELECT 
                    transaction_type,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count
                FROM {$this->table} 
                WHERE is_active = 1";
        
        $params = [];
        
        if ($start_date) {
            $sql .= " AND transaction_date >= :start_date";
            $params[':start_date'] = $start_date;
        }
        
        if ($end_date) {
            $sql .= " AND transaction_date <= :end_date";
            $params[':end_date'] = $end_date;
        }
        
        $sql .= " GROUP BY transaction_type";
        
        try {
            $stmt = $this->conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $results = $stmt->fetchAll();
            
            $summary = [
                'income' => ['total_amount' => 0, 'transaction_count' => 0],
                'expense' => ['total_amount' => 0, 'transaction_count' => 0],
                'balance' => 0
            ];
            
            foreach ($results as $result) {
                $summary[$result['transaction_type']] = $result;
            }
            
            $summary['balance'] = $summary['income']['total_amount'] - $summary['expense']['total_amount'];
            
            return $summary;
        } catch (PDOException $e) {
            error_log("Get finance summary error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get categories
     */
    public function getCategories($transaction_type = null) {
        $sql = "SELECT DISTINCT category 
                FROM {$this->table} 
                WHERE is_active = 1 AND category IS NOT NULL AND category != ''";
        
        if ($transaction_type) {
            $sql .= " AND transaction_type = :transaction_type";
        }
        
        $sql .= " ORDER BY category";
        
        try {
            $stmt = $this->conn->prepare($sql);
            if ($transaction_type) {
                $stmt->bindParam(':transaction_type', $transaction_type);
            }
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Get finance categories error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get monthly summary
     */
    public function getMonthlySummary($year = null) {
        if (!$year) {
            $year = date('Y');
        }
        
        $sql = "SELECT 
                    MONTH(transaction_date) as month,
                    transaction_type,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count
                FROM {$this->table} 
                WHERE is_active = 1 AND YEAR(transaction_date) = :year
                GROUP BY MONTH(transaction_date), transaction_type
                ORDER BY month";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->execute();
            $results = $stmt->fetchAll();
            
            $monthly_data = [];
            for ($i = 1; $i <= 12; $i++) {
                $monthly_data[$i] = [
                    'income' => ['total_amount' => 0, 'transaction_count' => 0],
                    'expense' => ['total_amount' => 0, 'transaction_count' => 0],
                    'balance' => 0
                ];
            }
            
            foreach ($results as $result) {
                $month = $result['month'];
                $type = $result['transaction_type'];
                $monthly_data[$month][$type] = [
                    'total_amount' => $result['total_amount'],
                    'transaction_count' => $result['transaction_count']
                ];
                $monthly_data[$month]['balance'] = 
                    $monthly_data[$month]['income']['total_amount'] - 
                    $monthly_data[$month]['expense']['total_amount'];
            }
            
            return $monthly_data;
        } catch (PDOException $e) {
            error_log("Get monthly finance summary error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get category summary
     */
    public function getCategorySummary($transaction_type = null, $start_date = null, $end_date = null) {
        $sql = "SELECT 
                    category,
                    SUM(amount) as total_amount,
                    COUNT(*) as transaction_count,
                    AVG(amount) as avg_amount
                FROM {$this->table} 
                WHERE is_active = 1";
        
        $params = [];
        
        if ($transaction_type) {
            $sql .= " AND transaction_type = :transaction_type";
            $params[':transaction_type'] = $transaction_type;
        }
        
        if ($start_date) {
            $sql .= " AND transaction_date >= :start_date";
            $params[':start_date'] = $start_date;
        }
        
        if ($end_date) {
            $sql .= " AND transaction_date <= :end_date";
            $params[':end_date'] = $end_date;
        }
        
        $sql .= " GROUP BY category ORDER BY total_amount DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get category summary error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Format amount for display
     */
    public static function formatAmount($amount) {
        return number_format($amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Get transaction type display
     */
    public static function getTypeDisplay($type) {
        return $type === 'income' ? 'Thu' : 'Chi';
    }

    /**
     * Search finance records
     */
    public function search($keyword, $filters = []) {
        $sql = "SELECT f.*, u.full_name as creator_name 
                FROM {$this->table} f 
                LEFT JOIN users u ON f.creator_id = u.id 
                WHERE f.is_active = 1 AND (
                    f.title LIKE :keyword OR 
                    f.description LIKE :keyword OR 
                    f.category LIKE :keyword
                )";
        
        $params = [':keyword' => "%{$keyword}%"];
        
        if (!empty($filters['transaction_type'])) {
            $sql .= " AND f.transaction_type = :transaction_type";
            $params[':transaction_type'] = $filters['transaction_type'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND f.transaction_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND f.transaction_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        $sql .= " ORDER BY f.transaction_date DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Search finance records error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent finance records
     */
    public function getRecent($limit = 10) {
        $sql = "SELECT f.*, u.full_name as creator_name
                FROM {$this->table} f
                LEFT JOIN users u ON f.creator_id = u.id
                WHERE f.is_active = 1
                ORDER BY f.created_at DESC
                LIMIT :limit";

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get recent finance records error: " . $e->getMessage());
            return [];
        }
    }
}
?>
