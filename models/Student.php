<?php
/**
 * Student Model
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class Student {
    private $conn;
    private $table = 'students';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Generate unique student code
     */
    public function generateStudentCode() {
        $base_code = 1000;
        
        // Get the highest existing student code
        $sql = "SELECT MAX(CAST(SUBSTRING(student_code, 1) AS UNSIGNED)) as max_code 
                FROM {$this->table} 
                WHERE student_code REGEXP '^[0-9]+$'";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result && $result['max_code']) {
                return $result['max_code'] + 1;
            }
            return $base_code;
        } catch (PDOException $e) {
            error_log("Generate student code error: " . $e->getMessage());
            return $base_code + rand(1, 999);
        }
    }

    /**
     * Create new student
     */
    public function create($data) {
        // Generate student code if not provided
        if (empty($data['student_code'])) {
            $data['student_code'] = $this->generateStudentCode();
        }

        $sql = "INSERT INTO {$this->table} 
                (student_code, full_name, date_of_birth, address, parent_name, parent_phone, 
                 profile_url, class_id, is_active, created_at) 
                VALUES (:student_code, :full_name, :date_of_birth, :address, :parent_name, 
                        :parent_phone, :profile_url, :class_id, :is_active, NOW())";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_code', $data['student_code']);
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':parent_name', $data['parent_name']);
            $stmt->bindParam(':parent_phone', $data['parent_phone']);
            $stmt->bindParam(':profile_url', $data['profile_url']);
            $stmt->bindParam(':class_id', $data['class_id']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Student creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get student by ID
     */
    public function getById($id) {
        $sql = "SELECT s.*, c.name as class_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                WHERE s.id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get student error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get student by code
     */
    public function getByCode($student_code) {
        $sql = "SELECT s.*, c.name as class_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                WHERE s.student_code = :student_code";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_code', $student_code);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get student by code error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all students
     */
    public function getAll($active_only = true, $class_id = null) {
        $sql = "SELECT s.*, c.name as class_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id";
        
        $conditions = [];
        if ($active_only) {
            $conditions[] = "s.is_active = 1";
        }
        if ($class_id) {
            $conditions[] = "s.class_id = :class_id";
        }
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        
        $sql .= " ORDER BY s.full_name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            if ($class_id) {
                $stmt->bindParam(':class_id', $class_id);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get all students error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update student
     */
    public function update($id, $data) {
        $sql = "UPDATE {$this->table} 
                SET full_name = :full_name, date_of_birth = :date_of_birth, 
                    address = :address, parent_name = :parent_name, parent_phone = :parent_phone,
                    profile_url = :profile_url, class_id = :class_id, is_active = :is_active,
                    updated_at = NOW()
                WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':parent_name', $data['parent_name']);
            $stmt->bindParam(':parent_phone', $data['parent_phone']);
            $stmt->bindParam(':profile_url', $data['profile_url']);
            $stmt->bindParam(':class_id', $data['class_id']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Student update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete student (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE {$this->table} SET is_active = 0, updated_at = NOW() WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Student delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get students by class
     */
    public function getByClass($class_id) {
        return $this->getAll(true, $class_id);
    }

    /**
     * Check if student code exists
     */
    public function codeExists($student_code, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE student_code = :student_code";
        
        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_code', $student_code);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Check student code exists error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get student attendance rate
     */
    public function getAttendanceRate($student_id) {
        $sql = "SELECT 
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_sessions
                FROM attendance 
                WHERE student_id = :student_id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_id', $student_id);
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result && $result['total_sessions'] > 0) {
                return round(($result['present_sessions'] / $result['total_sessions']) * 100, 2);
            }
            return 0;
        } catch (PDOException $e) {
            error_log("Get attendance rate error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Assign student to class
     */
    public function assignToClass($student_id, $class_id) {
        $sql = "UPDATE {$this->table} SET class_id = :class_id, updated_at = NOW() WHERE id = :student_id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_id', $student_id);
            $stmt->bindParam(':class_id', $class_id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Assign student to class error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Remove student from class
     */
    public function removeFromClass($student_id) {
        $sql = "UPDATE {$this->table} SET class_id = NULL, updated_at = NOW() WHERE id = :student_id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':student_id', $student_id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Remove student from class error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Search students
     */
    public function search($keyword, $class_id = null) {
        $sql = "SELECT s.*, c.name as class_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                WHERE s.is_active = 1 AND (
                    s.full_name LIKE :keyword OR 
                    s.student_code LIKE :keyword OR 
                    s.parent_name LIKE :keyword OR 
                    s.parent_phone LIKE :keyword
                )";
        
        if ($class_id) {
            $sql .= " AND s.class_id = :class_id";
        }
        
        $sql .= " ORDER BY s.full_name";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $search_term = "%{$keyword}%";
            $stmt->bindParam(':keyword', $search_term);
            if ($class_id) {
                $stmt->bindParam(':class_id', $class_id);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Search students error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get total count of students
     */
    public function getTotalCount($active_only = true) {
        $sql = "SELECT COUNT(*) FROM {$this->table}";

        if ($active_only) {
            $sql .= " WHERE is_active = 1";
        }

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Get total students count error: " . $e->getMessage());
            return 0;
        }
    }
}
?>
