#!/usr/bin/env python3
"""
Migration script from SQL to MongoDB for Vietnamese Classroom Management System
"""

import os
import sys
from datetime import datetime, date

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.class_model import Class
from app.models.student import Student
from app.models.schedule import Schedule
from app.models.attendance import Attendance
from app.models.event import Event
from app.models.finance import Finance
from app.models.expense import Expense, ExpenseCategory
from app.models.financial_transaction import DonationAsset

from app.models.mongo_models import (
    MongoUser, MongoClass, MongoStudent, MongoSchedule, 
    MongoStudentSchedule, MongoAttendance, MongoEvent, 
    MongoFinance, MongoExpense, MongoExpenseCategory, 
    MongoDonationAsset
)

def test_mongodb_connection():
    """Test MongoDB connection"""
    try:
        # Test connection by counting users
        user_count = MongoUser.objects.count()
        print(f"✅ MongoDB connected successfully! Current users: {user_count}")
        return True
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False

def clear_mongodb_collections():
    """Clear all MongoDB collections"""
    print("🧹 Clearing MongoDB collections...")
    
    collections = [
        MongoUser, MongoClass, MongoStudent, MongoSchedule,
        MongoStudentSchedule, MongoAttendance, MongoEvent,
        MongoFinance, MongoExpense, MongoExpenseCategory,
        MongoDonationAsset
    ]
    
    for collection in collections:
        try:
            collection.drop_collection()
            print(f"   Cleared {collection._get_collection_name()}")
        except Exception as e:
            print(f"   Warning: Could not clear {collection._get_collection_name()}: {e}")

def migrate_users():
    """Migrate users from SQL to MongoDB"""
    print("👥 Migrating users...")
    sql_users = User.query.all()
    
    user_mapping = {}  # Store SQL ID -> MongoDB Object mapping
    
    for sql_user in sql_users:
        try:
            mongo_user = MongoUser(
                username=sql_user.username,
                email=sql_user.email,
                password_hash=sql_user.password_hash,
                full_name=sql_user.full_name,
                phone=sql_user.phone,
                role=sql_user.role,
                is_active=sql_user.is_active,
                created_at=sql_user.created_at or datetime.utcnow()
            )
            mongo_user.save()
            user_mapping[sql_user.id] = mongo_user
            print(f"   ✅ Migrated user: {sql_user.username} ({sql_user.role})")
        except Exception as e:
            print(f"   ❌ Failed to migrate user {sql_user.username}: {e}")
    
    print(f"   📊 Total users migrated: {len(user_mapping)}")
    return user_mapping

def migrate_classes(user_mapping):
    """Migrate classes from SQL to MongoDB"""
    print("🏫 Migrating classes...")
    sql_classes = Class.query.all()
    
    class_mapping = {}  # Store SQL ID -> MongoDB Object mapping
    
    for sql_class in sql_classes:
        try:
            # Find manager in MongoDB
            manager = None
            if sql_class.manager_id and sql_class.manager_id in user_mapping:
                manager = user_mapping[sql_class.manager_id]
            
            mongo_class = MongoClass(
                name=sql_class.name,
                description=sql_class.description,
                manager=manager,
                is_active=sql_class.is_active,
                created_at=sql_class.created_at or datetime.utcnow()
            )
            mongo_class.save()
            class_mapping[sql_class.id] = mongo_class
            print(f"   ✅ Migrated class: {sql_class.name}")
        except Exception as e:
            print(f"   ❌ Failed to migrate class {sql_class.name}: {e}")
    
    print(f"   📊 Total classes migrated: {len(class_mapping)}")
    return class_mapping

def migrate_students(class_mapping):
    """Migrate students from SQL to MongoDB"""
    print("🎓 Migrating students...")
    sql_students = Student.query.all()
    
    student_mapping = {}  # Store SQL ID -> MongoDB Object mapping
    
    for sql_student in sql_students:
        try:
            # Find class in MongoDB
            class_ref = None
            if sql_student.class_id and sql_student.class_id in class_mapping:
                class_ref = class_mapping[sql_student.class_id]
            
            mongo_student = MongoStudent(
                student_code=sql_student.student_code,
                full_name=sql_student.full_name,
                date_of_birth=sql_student.date_of_birth,
                address=sql_student.address,
                parent_name=sql_student.parent_name,
                parent_phone=sql_student.parent_phone,
                profile_url=sql_student.profile_url,
                class_ref=class_ref,
                is_active=sql_student.is_active,
                created_at=sql_student.created_at or datetime.utcnow()
            )
            mongo_student.save()
            student_mapping[sql_student.id] = mongo_student
            print(f"   ✅ Migrated student: {sql_student.student_code} - {sql_student.full_name}")
        except Exception as e:
            print(f"   ❌ Failed to migrate student {sql_student.student_code}: {e}")
    
    print(f"   📊 Total students migrated: {len(student_mapping)}")
    return student_mapping

def migrate_schedules(class_mapping, user_mapping):
    """Migrate schedules from SQL to MongoDB"""
    print("📅 Migrating schedules...")
    sql_schedules = Schedule.query.all()
    
    schedule_mapping = {}  # Store SQL ID -> MongoDB Object mapping
    
    for sql_schedule in sql_schedules:
        try:
            # Find class and teacher in MongoDB
            class_ref = None
            if sql_schedule.class_id and sql_schedule.class_id in class_mapping:
                class_ref = class_mapping[sql_schedule.class_id]
            
            teacher = None
            if sql_schedule.teacher_id and sql_schedule.teacher_id in user_mapping:
                teacher = user_mapping[sql_schedule.teacher_id]
            
            if not class_ref or not teacher:
                print(f"   ⚠️  Skipping schedule - missing class or teacher reference")
                continue
            
            # Convert time objects to string format
            start_time = sql_schedule.start_time.strftime('%H:%M') if sql_schedule.start_time else '08:00'
            end_time = sql_schedule.end_time.strftime('%H:%M') if sql_schedule.end_time else '09:00'
            
            mongo_schedule = MongoSchedule(
                class_ref=class_ref,
                teacher=teacher,
                day_of_week=sql_schedule.day_of_week,
                session=sql_schedule.session,
                start_time=start_time,
                end_time=end_time,
                subject=sql_schedule.subject,
                room=sql_schedule.room,
                week_number=sql_schedule.week_number or MongoSchedule.get_current_week(),
                week_created=sql_schedule.week_created or MongoSchedule.get_current_week(),
                is_active=sql_schedule.is_active,
                created_at=sql_schedule.created_at or datetime.utcnow()
            )
            mongo_schedule.save()
            schedule_mapping[sql_schedule.id] = mongo_schedule
            print(f"   ✅ Migrated schedule: {class_ref.name} - {teacher.full_name} - Week {mongo_schedule.week_number}")
        except Exception as e:
            print(f"   ❌ Failed to migrate schedule: {e}")
    
    print(f"   📊 Total schedules migrated: {len(schedule_mapping)}")
    return schedule_mapping

def migrate_attendance(student_mapping, schedule_mapping):
    """Migrate attendance from SQL to MongoDB"""
    print("✅ Migrating attendance...")
    sql_attendance = Attendance.query.all()
    
    migrated_count = 0
    
    for sql_att in sql_attendance:
        try:
            # Find student and schedule in MongoDB
            student = None
            if sql_att.student_id and sql_att.student_id in student_mapping:
                student = student_mapping[sql_att.student_id]
            
            schedule = None
            if sql_att.schedule_id and sql_att.schedule_id in schedule_mapping:
                schedule = schedule_mapping[sql_att.schedule_id]
            
            if not student or not schedule:
                continue
            
            mongo_attendance = MongoAttendance(
                student=student,
                schedule=schedule,
                date=sql_att.date,
                status=sql_att.status,
                notes=sql_att.notes,
                created_at=sql_att.created_at or datetime.utcnow()
            )
            mongo_attendance.save()
            migrated_count += 1
            
            if migrated_count % 50 == 0:  # Progress indicator
                print(f"   📊 Migrated {migrated_count} attendance records...")
                
        except Exception as e:
            print(f"   ❌ Failed to migrate attendance record: {e}")
    
    print(f"   📊 Total attendance records migrated: {migrated_count}")

def create_default_admin():
    """Create default admin user if not exists"""
    print("👑 Creating default admin user...")
    
    admin = MongoUser.objects(username='admin').first()
    if not admin:
        admin = MongoUser(
            username='admin',
            email='<EMAIL>',
            full_name='Administrator',
            role='admin',
            is_active=True
        )
        admin.set_password('admin123')
        admin.save()
        print("   ✅ Default admin user created: admin/admin123")
    else:
        print("   ℹ️  Admin user already exists")

def run_migration():
    """Run full migration from SQL to MongoDB"""
    print("🚀 Starting migration from SQL to MongoDB...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        # Test MongoDB connection
        if not test_mongodb_connection():
            print("❌ Cannot proceed without MongoDB connection")
            return False
        
        print("\n" + "=" * 60)
        
        # Clear existing MongoDB data
        clear_mongodb_collections()
        
        print("\n" + "=" * 60)
        
        try:
            # Run migrations in order
            user_mapping = migrate_users()
            class_mapping = migrate_classes(user_mapping)
            student_mapping = migrate_students(class_mapping)
            schedule_mapping = migrate_schedules(class_mapping, user_mapping)
            migrate_attendance(student_mapping, schedule_mapping)
            
            # Create default admin
            create_default_admin()
            
            print("\n" + "=" * 60)
            print("🎉 Migration completed successfully!")
            print("\n📊 Migration Summary:")
            print(f"   Users: {len(user_mapping)}")
            print(f"   Classes: {len(class_mapping)}")
            print(f"   Students: {len(student_mapping)}")
            print(f"   Schedules: {len(schedule_mapping)}")
            print(f"   Total MongoDB documents: {MongoUser.objects.count() + MongoClass.objects.count() + MongoStudent.objects.count() + MongoSchedule.objects.count()}")
            
            print("\n🔑 Default Login Credentials:")
            print("   Username: admin")
            print("   Password: admin123")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🍃 Vietnamese Classroom Management - SQL to MongoDB Migration")
    print("=" * 60)
    
    # Confirm before proceeding
    response = input("⚠️  This will clear all existing MongoDB data. Continue? (y/N): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        sys.exit(0)
    
    success = run_migration()
    
    if success:
        print("\n✅ Migration completed! You can now use MongoDB as your primary database.")
        print("💡 Next steps:")
        print("   1. Test the application with MongoDB")
        print("   2. Update your routes to use MongoDB models")
        print("   3. Remove SQL dependencies when ready")
    else:
        print("\n❌ Migration failed. Please check the errors above.")
        sys.exit(1)
