<?php
/**
 * Authentication System
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Login user
     */
    public function login($username, $password) {
        try {
            $conn = $this->db->getConnection();
            
            $sql = "SELECT * FROM users WHERE username = ? AND is_active = 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // Start session and store user data
                session_start();
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['logged_in'] = true;
                $_SESSION['login_time'] = time();
                
                // Update last login
                $updateSql = "UPDATE users SET updated_at = NOW() WHERE id = ?";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->execute([$user['id']]);
                
                // Log activity
                logActivity($user['id'], 'login', 'User logged in');
                
                return [
                    'success' => true,
                    'user' => $user,
                    'message' => 'Đăng nhập thành công'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Tên đăng nhập hoặc mật khẩu không đúng'
                ];
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi đăng nhập'
            ];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        session_start();
        
        if (isset($_SESSION['user_id'])) {
            logActivity($_SESSION['user_id'], 'logout', 'User logged out');
        }
        
        // Destroy session
        session_unset();
        session_destroy();
        
        return [
            'success' => true,
            'message' => 'Đăng xuất thành công'
        ];
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        session_start();
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }
    
    /**
     * Get current user
     */
    public function getCurrentUser() {
        session_start();
        
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        try {
            $conn = $this->db->getConnection();
            $sql = "SELECT * FROM users WHERE id = ? AND is_active = 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$_SESSION['user_id']]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Get current user error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        session_start();
        return isset($_SESSION['role']) && $_SESSION['role'] === $role;
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin() {
        return $this->hasRole('admin');
    }
    
    /**
     * Check if user is manager
     */
    public function isManager() {
        return $this->hasRole('manager');
    }
    
    /**
     * Check if user is teacher
     */
    public function isTeacher() {
        return $this->hasRole('teacher');
    }
    
    /**
     * Check if user has admin or manager role
     */
    public function isAdminOrManager() {
        return $this->isAdmin() || $this->isManager();
    }
    
    /**
     * Require login
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: /auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
            exit;
        }
    }
    
    /**
     * Require admin role
     */
    public function requireAdmin() {
        $this->requireLogin();
        if (!$this->isAdmin()) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này';
            header('Location: /dashboard.php');
            exit;
        }
    }
    
    /**
     * Require manager role (admin or manager)
     */
    public function requireManager() {
        $this->requireLogin();
        if (!$this->isAdminOrManager()) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này';
            header('Location: /dashboard.php');
            exit;
        }
    }
    
    /**
     * Require teacher role
     */
    public function requireTeacher() {
        $this->requireLogin();
        if (!$this->isTeacher()) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này';
            header('Location: /dashboard.php');
            exit;
        }
    }
    
    /**
     * Register new user (admin only)
     */
    public function register($userData) {
        try {
            $conn = $this->db->getConnection();
            
            // Check if username or email already exists
            $checkSql = "SELECT id FROM users WHERE username = ? OR email = ?";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->execute([$userData['username'], $userData['email']]);
            
            if ($checkStmt->fetch()) {
                return [
                    'success' => false,
                    'message' => 'Tên đăng nhập hoặc email đã tồn tại'
                ];
            }
            
            // Hash password
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // Insert new user
            $sql = "INSERT INTO users (username, email, password_hash, full_name, phone, role, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $params = [
                $userData['username'],
                $userData['email'],
                $passwordHash,
                $userData['full_name'],
                $userData['phone'] ?? null,
                $userData['role'] ?? 'user',
                $userData['is_active'] ?? 1
            ];
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            
            $userId = $conn->lastInsertId();
            
            // Log activity
            if (isset($_SESSION['user_id'])) {
                logActivity($_SESSION['user_id'], 'user_created', "Created user: {$userData['username']}");
            }
            
            return [
                'success' => true,
                'user_id' => $userId,
                'message' => 'Tạo người dùng thành công'
            ];
            
        } catch (Exception $e) {
            error_log("Register error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo người dùng'
            ];
        }
    }
    
    /**
     * Update user password
     */
    public function updatePassword($userId, $newPassword) {
        try {
            $conn = $this->db->getConnection();
            
            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            
            $sql = "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$passwordHash, $userId]);
            
            // Log activity
            logActivity($userId, 'password_changed', 'Password updated');
            
            return [
                'success' => true,
                'message' => 'Cập nhật mật khẩu thành công'
            ];
            
        } catch (Exception $e) {
            error_log("Update password error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật mật khẩu'
            ];
        }
    }
    
    /**
     * Get user permissions
     */
    public function getUserPermissions() {
        session_start();
        
        if (!$this->isLoggedIn()) {
            return [];
        }
        
        $role = $_SESSION['role'];
        $permissions = [];
        
        switch ($role) {
            case 'admin':
                $permissions = [
                    'user_management',
                    'class_management',
                    'student_management',
                    'schedule_management',
                    'attendance_management',
                    'finance_management',
                    'expense_management',
                    'report_access',
                    'system_settings'
                ];
                break;
                
            case 'manager':
                $permissions = [
                    'class_management',
                    'student_management',
                    'schedule_management',
                    'attendance_management',
                    'finance_management',
                    'expense_management',
                    'report_access'
                ];
                break;
                
            case 'teacher':
                $permissions = [
                    'schedule_view',
                    'attendance_management',
                    'student_view'
                ];
                break;
                
            default:
                $permissions = ['basic_access'];
        }
        
        return $permissions;
    }
    
    /**
     * Check if user has specific permission
     */
    public function hasPermission($permission) {
        $permissions = $this->getUserPermissions();
        return in_array($permission, $permissions);
    }
}


?>
