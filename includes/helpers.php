<?php
/**
 * Helper Functions
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/auth.php';

// Initialize Auth instance
$auth = new Auth();

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    global $auth;
    return $auth->isLoggedIn();
}

/**
 * Get current logged in user
 */
function getCurrentUser() {
    global $auth;
    return $auth->getCurrentUser();
}

/**
 * Login user
 */
function login($username, $password) {
    global $auth;
    return $auth->login($username, $password);
}

/**
 * Logout user
 */
function logout() {
    global $auth;
    return $auth->logout();
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    $user = getCurrentUser();
    return $user && $user['role'] === $role;
}

/**
 * Check if user has admin role
 */
function isAdmin() {
    return hasRole('admin');
}

/**
 * Check if user has manager role
 */
function isManager() {
    return hasRole('manager');
}

/**
 * Check if user has teacher role
 */
function isTeacher() {
    return hasRole('teacher');
}

/**
 * Require login
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /auth/login.php');
        exit;
    }
}

/**
 * Require specific role
 */
function requireRole($role) {
    requireLogin();
    if (!hasRole($role)) {
        header('HTTP/1.0 403 Forbidden');
        die('Access denied');
    }
}

/**
 * Require admin role
 */
function requireAdmin() {
    requireRole('admin');
}

/**
 * Require manager or admin role
 */
function requireManagerOrAdmin() {
    requireLogin();
    $user = getCurrentUser();
    if (!$user || !in_array($user['role'], ['admin', 'manager'])) {
        header('HTTP/1.0 403 Forbidden');
        die('Access denied');
    }
}

/**
 * Set notification message
 */
function setNotification($message, $type = 'success') {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['notification'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Generate random password
 */
function generateRandomPassword($length = 8) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $password;
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Format currency
 */
function formatCurrency($amount) {
    return number_format($amount, 0, ',', '.') . ' VNĐ';
}

/**
 * Format date
 */
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * Format datetime
 */
function formatDateTime($datetime, $format = 'd/m/Y H:i') {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * Get Vietnamese day name
 */
function getVietnameseDayName($day_of_week) {
    $days = [
        1 => 'Thứ 2',
        2 => 'Thứ 3',
        3 => 'Thứ 4',
        4 => 'Thứ 5',
        5 => 'Thứ 6',
        6 => 'Thứ 7',
        7 => 'Chủ nhật'
    ];
    return $days[$day_of_week] ?? '';
}

/**
 * Get session name
 */
function getSessionName($session) {
    $sessions = [
        'morning' => 'Buổi sáng',
        'afternoon' => 'Buổi chiều',
        'evening' => 'Buổi tối'
    ];
    return $sessions[$session] ?? $session;
}

/**
 * Get attendance status display
 */
function getAttendanceStatusDisplay($status) {
    $statuses = [
        'present' => 'Có mặt',
        'absent' => 'Vắng mặt',
        'late' => 'Đi muộn',
        'excused' => 'Vắng có phép'
    ];
    return $statuses[$status] ?? $status;
}

/**
 * Get role display name
 */
function getRoleDisplay($role) {
    $roles = [
        'admin' => 'Quản trị viên',
        'manager' => 'Quản sinh',
        'teacher' => 'Giáo viên',
        'user' => 'Người dùng'
    ];
    return $roles[$role] ?? $role;
}

/**
 * Generate student code
 */
function generateStudentCode() {
    // Get current timestamp and add random number
    $timestamp = time();
    $random = rand(100, 999);
    return $timestamp . $random;
}

/**
 * Validate Vietnamese phone number
 */
function isValidVietnamesePhone($phone) {
    // Remove spaces and special characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Vietnamese phone number
    return preg_match('/^(0[3|5|7|8|9])[0-9]{8}$/', $phone);
}

/**
 * Log activity
 */
function logActivity($user_id, $action, $description) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $sql = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, created_at) 
                VALUES (:user_id, :action, :description, :ip_address, :user_agent, NOW())";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR'] ?? '');
        $stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
        
        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Log activity error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get current week in YYYY-WXX format
 */
function getCurrentWeek() {
    $date = new DateTime();
    $year = $date->format('Y');
    $week = $date->format('W');
    return "{$year}-W{$week}";
}

/**
 * Get week dates
 */
function getWeekDates($week_string) {
    // Parse week string like "2025-W25"
    if (preg_match('/(\d{4})-W(\d{2})/', $week_string, $matches)) {
        $year = $matches[1];
        $week = $matches[2];
        
        $date = new DateTime();
        $date->setISODate($year, $week);
        
        $start_date = clone $date;
        $end_date = clone $date;
        $end_date->modify('+6 days');
        
        return [
            'start' => $start_date->format('Y-m-d'),
            'end' => $end_date->format('Y-m-d'),
            'start_formatted' => $start_date->format('d/m/Y'),
            'end_formatted' => $end_date->format('d/m/Y')
        ];
    }
    
    return null;
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    setNotification($message, $type);
    header("Location: $url");
    exit;
}

/**
 * Check CSRF token
 */
function checkCSRF($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Generate CSRF token
 */
function generateCSRF() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION['csrf_token'];
}
?>
