<?php
/**
 * Attendance Management Page
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../models/Attendance.php';
require_once __DIR__ . '/../models/Schedule.php';
require_once __DIR__ . '/../models/Student.php';

$attendanceModel = new Attendance();
$scheduleModel = new Schedule();
$studentModel = new Student();
$action = $_GET['action'] ?? 'list';
$schedule_id = $_GET['schedule_id'] ?? null;
$date = $_GET['date'] ?? date('Y-m-d');
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'save_attendance') {
        $schedule_id = $_POST['schedule_id'];
        $date = $_POST['date'];
        $attendance_data = $_POST['attendance'] ?? [];
        
        $success_count = 0;
        foreach ($attendance_data as $student_id => $data) {
            $attendanceRecord = [
                'schedule_id' => $schedule_id,
                'student_id' => $student_id,
                'date' => $date,
                'status' => $data['status'] ?? 'present',
                'reason' => $data['reason'] ?? '',
                'check_in_time' => !empty($data['check_in_time']) ? $date . ' ' . $data['check_in_time'] : null,
                'check_out_time' => !empty($data['check_out_time']) ? $date . ' ' . $data['check_out_time'] : null,
                'lesson_content' => $data['lesson_content'] ?? '',
                'notes' => $data['notes'] ?? ''
            ];
            
            // Check if attendance already exists
            if ($attendanceModel->exists($schedule_id, $student_id, $date)) {
                // Update existing record
                $existing = $attendanceModel->getByScheduleAndDate($schedule_id, $date);
                foreach ($existing as $record) {
                    if ($record['student_id'] == $student_id) {
                        if ($attendanceModel->update($record['id'], $attendanceRecord)) {
                            $success_count++;
                        }
                        break;
                    }
                }
            } else {
                // Create new record
                if ($attendanceModel->create($attendanceRecord)) {
                    $success_count++;
                }
            }
        }
        
        if ($success_count > 0) {
            $message = "Đã lưu điểm danh cho $success_count học sinh!";
        } else {
            $error = 'Có lỗi xảy ra khi lưu điểm danh!';
        }
    }
}

// Get data for display
if ($schedule_id) {
    $schedule = $scheduleModel->getById($schedule_id);
    if ($schedule) {
        $students = $studentModel->getByClass($schedule['class_id']);
        $existing_attendance = $attendanceModel->getByScheduleAndDate($schedule_id, $date);
        
        // Create attendance lookup array
        $attendance_lookup = [];
        foreach ($existing_attendance as $record) {
            $attendance_lookup[$record['student_id']] = $record;
        }
    }
} else {
    // Get recent schedules for selection
    if ($current_user['role'] === 'teacher') {
        $recent_schedules = $scheduleModel->getByTeacher($current_user['id'], getCurrentWeek());
    } else {
        $recent_schedules = $scheduleModel->getByWeek(getCurrentWeek());
    }
}
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Điểm danh</h1>
        <?php if ($schedule_id): ?>
        <a href="?page=attendance" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Chọn lịch khác
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?= htmlspecialchars($message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if (!$schedule_id): ?>
    <!-- Schedule Selection -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Chọn lịch học để điểm danh</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($recent_schedules as $schedule): ?>
                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div class="font-medium text-gray-900"><?= htmlspecialchars($schedule['class_name']) ?></div>
                    <div class="text-sm text-gray-600"><?= htmlspecialchars($schedule['subject']) ?></div>
                    <div class="text-sm text-gray-600">
                        <?= getVietnameseDayName($schedule['day_of_week']) ?> • 
                        <?= date('H:i', strtotime($schedule['start_time'])) ?> - 
                        <?= date('H:i', strtotime($schedule['end_time'])) ?>
                    </div>
                    <div class="text-sm text-gray-600"><?= htmlspecialchars($schedule['teacher_name']) ?></div>
                    
                    <div class="mt-3">
                        <a href="?page=attendance&schedule_id=<?= $schedule['id'] ?>&date=<?= date('Y-m-d') ?>" 
                           class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition-colors">
                            Điểm danh hôm nay
                        </a>
                        <a href="?page=attendance&schedule_id=<?= $schedule['id'] ?>&date=<?= date('Y-m-d', strtotime('-1 day')) ?>" 
                           class="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400 transition-colors ml-2">
                            Hôm qua
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <?php else: ?>
    <!-- Attendance Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        Điểm danh: <?= htmlspecialchars($schedule['class_name']) ?>
                    </h3>
                    <p class="text-sm text-gray-600">
                        <?= htmlspecialchars($schedule['subject']) ?> • 
                        <?= getVietnameseDayName($schedule['day_of_week']) ?> • 
                        <?= date('H:i', strtotime($schedule['start_time'])) ?> - 
                        <?= date('H:i', strtotime($schedule['end_time'])) ?> • 
                        <?= formatDate($date) ?>
                    </p>
                </div>
                
                <div class="flex space-x-2">
                    <input type="date" value="<?= $date ?>" onchange="changeDate(this.value)" 
                           class="border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                </div>
            </div>
            
            <form method="POST">
                <input type="hidden" name="action" value="save_attendance">
                <input type="hidden" name="schedule_id" value="<?= $schedule_id ?>">
                <input type="hidden" name="date" value="<?= $date ?>">
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã HS</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Họ tên</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lý do</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giờ vào</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giờ ra</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($students as $student): ?>
                            <?php $attendance = $attendance_lookup[$student['id']] ?? null; ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <?= htmlspecialchars($student['student_code']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= htmlspecialchars($student['full_name']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <select name="attendance[<?= $student['id'] ?>][status]" 
                                            class="border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                                        <option value="present" <?= ($attendance['status'] ?? 'present') === 'present' ? 'selected' : '' ?>>Có mặt</option>
                                        <option value="absent_with_reason" <?= ($attendance['status'] ?? '') === 'absent_with_reason' ? 'selected' : '' ?>>Vắng có lý do</option>
                                        <option value="absent_without_reason" <?= ($attendance['status'] ?? '') === 'absent_without_reason' ? 'selected' : '' ?>>Vắng không lý do</option>
                                    </select>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <input type="text" name="attendance[<?= $student['id'] ?>][reason]" 
                                           value="<?= htmlspecialchars($attendance['reason'] ?? '') ?>"
                                           class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <input type="time" name="attendance[<?= $student['id'] ?>][check_in_time]" 
                                           value="<?= $attendance['check_in_time'] ? date('H:i', strtotime($attendance['check_in_time'])) : '' ?>"
                                           class="border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <input type="time" name="attendance[<?= $student['id'] ?>][check_out_time]" 
                                           value="<?= $attendance['check_out_time'] ? date('H:i', strtotime($attendance['check_out_time'])) : '' ?>"
                                           class="border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <input type="text" name="attendance[<?= $student['id'] ?>][notes]" 
                                           value="<?= htmlspecialchars($attendance['notes'] ?? '') ?>"
                                           class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-6 flex justify-between">
                    <div class="flex space-x-2">
                        <button type="button" onclick="markAllPresent()" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition-colors">
                            Có mặt tất cả
                        </button>
                        <button type="button" onclick="setCurrentTime()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors">
                            Giờ hiện tại
                        </button>
                    </div>
                    
                    <button type="submit" class="bg-primary text-white px-6 py-2 rounded-md hover:bg-secondary transition-colors">
                        <i class="fas fa-save mr-2"></i>Lưu điểm danh
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function changeDate(date) {
    const url = new URL(window.location);
    url.searchParams.set('date', date);
    window.location.href = url.toString();
}

function markAllPresent() {
    const statusSelects = document.querySelectorAll('select[name*="[status]"]');
    statusSelects.forEach(select => {
        select.value = 'present';
    });
}

function setCurrentTime() {
    const now = new Date();
    const timeString = now.toTimeString().slice(0, 5);
    
    const checkInInputs = document.querySelectorAll('input[name*="[check_in_time]"]');
    checkInInputs.forEach(input => {
        if (!input.value) {
            input.value = timeString;
        }
    });
}
</script>
