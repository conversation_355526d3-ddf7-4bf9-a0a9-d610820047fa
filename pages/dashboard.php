<?php
/**
 * Dashboard Page
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Class.php';
require_once __DIR__ . '/../models/Student.php';
require_once __DIR__ . '/../models/Schedule.php';
require_once __DIR__ . '/../models/Attendance.php';
require_once __DIR__ . '/../models/Finance.php';

$current_user = getCurrentUser();

// Initialize models
$userModel = new User();
$classModel = new ClassModel();
$studentModel = new Student();
$scheduleModel = new Schedule();
$attendanceModel = new Attendance();
$financeModel = new Finance();

// Get statistics based on user role
$stats = [];

if ($current_user['role'] === 'admin') {
    $stats = [
        'total_users' => $userModel->getTotalCount(),
        'total_classes' => $classModel->getTotalCount(),
        'total_students' => $studentModel->getTotalCount(),
        'total_schedules' => $scheduleModel->getTotalCount(),
        'recent_finances' => $financeModel->getRecent(5)
    ];
} elseif ($current_user['role'] === 'manager') {
    $stats = [
        'my_classes' => $classModel->getByManager($current_user['id']),
        'total_students' => $studentModel->getTotalCount(),
        'current_week_schedules' => $scheduleModel->getByWeek(getCurrentWeek()),
        'recent_finances' => $financeModel->getRecent(5)
    ];
} elseif ($current_user['role'] === 'teacher') {
    $stats = [
        'my_schedules' => $scheduleModel->getByTeacher($current_user['id'], getCurrentWeek()),
        'my_classes' => $scheduleModel->getTeacherClasses($current_user['id']),
        'recent_attendance' => $attendanceModel->getRecentByTeacher($current_user['id'], 10)
    ];
}

// Get current week info
$current_week = getCurrentWeek();
$week_dates = getWeekDates($current_week);
?>

<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-12 w-12 rounded-full bg-primary flex items-center justify-center">
                        <span class="text-white text-lg font-medium">
                            <?= strtoupper(substr($current_user['full_name'], 0, 1)) ?>
                        </span>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-2xl font-bold text-gray-900">
                        Chào mừng, <?= htmlspecialchars($current_user['full_name']) ?>!
                    </h1>
                    <p class="text-sm text-gray-500">
                        <?= getRoleDisplay($current_user['role']) ?> • 
                        Tuần hiện tại: <?= $current_week ?> 
                        (<?= $week_dates['start_formatted'] ?? '' ?> - <?= $week_dates['end_formatted'] ?? '' ?>)
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <?php if ($current_user['role'] === 'admin'): ?>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-2xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng người dùng</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= $stats['total_users'] ?? 0 ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=users" class="font-medium text-primary hover:text-secondary">Xem tất cả</a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chalkboard text-2xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng lớp học</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= $stats['total_classes'] ?? 0 ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=classes" class="font-medium text-primary hover:text-secondary">Xem tất cả</a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-graduate text-2xl text-yellow-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng học sinh</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= $stats['total_students'] ?? 0 ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=students" class="font-medium text-primary hover:text-secondary">Xem tất cả</a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt text-2xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng lịch học</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= $stats['total_schedules'] ?? 0 ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=schedules" class="font-medium text-primary hover:text-secondary">Xem tất cả</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($current_user['role'] === 'manager'): ?>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chalkboard text-2xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Lớp học của tôi</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= count($stats['my_classes'] ?? []) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=classes" class="font-medium text-primary hover:text-secondary">Quản lý lớp học</a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-graduate text-2xl text-yellow-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng học sinh</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= $stats['total_students'] ?? 0 ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=students" class="font-medium text-primary hover:text-secondary">Quản lý học sinh</a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-week text-2xl text-purple-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Lịch tuần này</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= count($stats['current_week_schedules'] ?? []) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=schedules" class="font-medium text-primary hover:text-secondary">Xem lịch học</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($current_user['role'] === 'teacher'): ?>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt text-2xl text-blue-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Lịch dạy tuần này</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= count($stats['my_schedules'] ?? []) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=schedules" class="font-medium text-primary hover:text-secondary">Xem lịch dạy</a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chalkboard-teacher text-2xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Lớp học của tôi</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= count($stats['my_classes'] ?? []) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=attendance" class="font-medium text-primary hover:text-secondary">Điểm danh</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Thao tác nhanh</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
                <a href="?page=students&action=add" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-user-plus text-2xl text-primary mb-2"></i>
                    <span class="text-sm font-medium text-gray-900">Thêm học sinh</span>
                </a>
                <a href="?page=classes&action=add" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-plus-circle text-2xl text-primary mb-2"></i>
                    <span class="text-sm font-medium text-gray-900">Tạo lớp học</span>
                </a>
                <a href="?page=schedules&action=add" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-calendar-plus text-2xl text-primary mb-2"></i>
                    <span class="text-sm font-medium text-gray-900">Tạo lịch học</span>
                </a>
                <?php endif; ?>
                <a href="?page=attendance" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-check-circle text-2xl text-primary mb-2"></i>
                    <span class="text-sm font-medium text-gray-900">Điểm danh</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity or Schedule -->
    <?php if ($current_user['role'] === 'teacher' && !empty($stats['my_schedules'])): ?>
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Lịch dạy tuần này</h3>
            <div class="space-y-3">
                <?php foreach ($stats['my_schedules'] as $schedule): ?>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <div class="font-medium text-gray-900"><?= htmlspecialchars($schedule['class_name']) ?></div>
                        <div class="text-sm text-gray-500">
                            <?= getVietnameseDayName($schedule['day_of_week']) ?> • 
                            <?= getSessionName($schedule['session']) ?> • 
                            <?= date('H:i', strtotime($schedule['start_time'])) ?> - <?= date('H:i', strtotime($schedule['end_time'])) ?>
                        </div>
                    </div>
                    <a href="?page=attendance&schedule_id=<?= $schedule['id'] ?>" class="text-primary hover:text-secondary">
                        <i class="fas fa-check-circle"></i>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
