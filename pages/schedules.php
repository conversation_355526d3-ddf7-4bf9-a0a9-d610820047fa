<?php
/**
 * Schedules Management Page
 * Vietnamese Classroom Management System
 */

// Check permission
if (!in_array($current_user['role'], ['admin', 'manager', 'teacher'])) {
    header('HTTP/1.0 403 Forbidden');
    die('<div class="text-center py-12"><h1 class="text-2xl font-bold text-red-600">T<PERSON>y cập bị từ chối</h1><p class="text-gray-600">Bạn không có quyền truy cập trang này.</p></div>');
}

require_once __DIR__ . '/../models/Schedule.php';
require_once __DIR__ . '/../models/Class.php';
require_once __DIR__ . '/../models/User.php';

$scheduleModel = new Schedule();
$classModel = new ClassModel();
$userModel = new User();
$action = $_GET['action'] ?? 'list';
$week_number = $_GET['week'] ?? getCurrentWeek();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $scheduleData = [
            'class_id' => $_POST['class_id'] ?? 0,
            'teacher_id' => $_POST['teacher_id'] ?? 0,
            'day_of_week' => $_POST['day_of_week'] ?? 1,
            'session' => $_POST['session'] ?? 'morning',
            'start_time' => $_POST['start_time'] ?? '',
            'end_time' => $_POST['end_time'] ?? '',
            'subject' => $_POST['subject'] ?? '',
            'room' => $_POST['room'] ?? '',
            'week_number' => $_POST['week_number'] ?? getCurrentWeek(),
            'week_created' => getCurrentWeek(),
            'is_active' => 1
        ];
        
        if ($scheduleModel->create($scheduleData)) {
            $message = 'Thêm lịch học thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi thêm lịch học!';
        }
    } elseif ($action === 'delete') {
        $id = $_POST['id'] ?? 0;
        if ($scheduleModel->delete($id)) {
            $message = 'Xóa lịch học thành công!';
        } else {
            $error = 'Có lỗi xảy ra khi xóa lịch học!';
        }
        $action = 'list';
    } elseif ($action === 'copy_week') {
        $from_week = $_POST['from_week'] ?? '';
        $to_week = $_POST['to_week'] ?? '';
        
        if ($scheduleModel->copyWeekSchedules($from_week, $to_week)) {
            $message = "Sao chép lịch từ tuần $from_week sang tuần $to_week thành công!";
            $week_number = $to_week;
        } else {
            $error = 'Có lỗi xảy ra khi sao chép lịch!';
        }
        $action = 'list';
    }
}

// Get data for display
if ($action === 'list') {
    if ($current_user['role'] === 'teacher') {
        $schedules = $scheduleModel->getByTeacher($current_user['id'], $week_number);
    } else {
        $schedules = $scheduleModel->getByWeek($week_number);
    }
    
    // Organize schedules by day
    $weekly_schedule = [];
    foreach ($schedules as $schedule) {
        $weekly_schedule[$schedule['day_of_week']][] = $schedule;
    }
}

// Get available weeks
$available_weeks = $scheduleModel->getAvailableWeeks();

// Get classes and teachers for dropdowns
$classes = $classModel->getAll();
$teachers = $userModel->getByRole('teacher');

// Get week dates
$week_dates = getWeekDates($week_number);
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Quản lý lịch học</h1>
            <p class="text-gray-600">
                Tuần <?= $week_number ?> 
                <?php if ($week_dates): ?>
                (<?= $week_dates['start_formatted'] ?> - <?= $week_dates['end_formatted'] ?>)
                <?php endif; ?>
            </p>
        </div>
        <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
        <div class="flex space-x-2">
            <button onclick="showCopyWeekModal()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors">
                <i class="fas fa-copy mr-2"></i>Sao chép tuần
            </button>
            <a href="?page=schedules&action=add&week=<?= $week_number ?>" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                <i class="fas fa-plus mr-2"></i>Thêm lịch học
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- Week Navigation -->
    <div class="bg-white shadow rounded-lg p-4">
        <div class="flex justify-between items-center">
            <div class="flex space-x-2">
                <select onchange="changeWeek(this.value)" class="border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    <option value="">Chọn tuần</option>
                    <?php foreach ($available_weeks as $week): ?>
                    <option value="<?= $week ?>" <?= $week === $week_number ? 'selected' : '' ?>>
                        Tuần <?= $week ?>
                    </option>
                    <?php endforeach; ?>
                </select>
                <a href="?page=schedules&week=<?= getCurrentWeek() ?>" class="bg-gray-300 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-400 transition-colors">
                    Tuần hiện tại
                </a>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?= htmlspecialchars($message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Weekly Schedule Grid -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="grid grid-cols-7 gap-0">
            <!-- Days Header -->
            <?php for ($day = 1; $day <= 7; $day++): ?>
            <div class="bg-gray-50 px-4 py-3 text-center font-medium text-gray-900 border-r border-gray-200">
                <?= getVietnameseDayName($day) ?>
            </div>
            <?php endfor; ?>
            
            <!-- Schedule Content -->
            <?php for ($day = 1; $day <= 7; $day++): ?>
            <div class="min-h-96 p-2 border-r border-b border-gray-200">
                <?php if (isset($weekly_schedule[$day])): ?>
                    <?php foreach ($weekly_schedule[$day] as $schedule): ?>
                    <div class="bg-blue-100 border border-blue-300 rounded p-2 mb-2 text-xs">
                        <div class="font-medium text-blue-900"><?= htmlspecialchars($schedule['class_name']) ?></div>
                        <div class="text-blue-700"><?= htmlspecialchars($schedule['subject']) ?></div>
                        <div class="text-blue-600">
                            <?= date('H:i', strtotime($schedule['start_time'])) ?> - 
                            <?= date('H:i', strtotime($schedule['end_time'])) ?>
                        </div>
                        <div class="text-blue-600"><?= htmlspecialchars($schedule['teacher_name']) ?></div>
                        <?php if ($schedule['room']): ?>
                        <div class="text-blue-600">Phòng: <?= htmlspecialchars($schedule['room']) ?></div>
                        <?php endif; ?>
                        
                        <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
                        <div class="mt-2 flex space-x-1">
                            <a href="?page=attendance&schedule_id=<?= $schedule['id'] ?>" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-check-circle"></i>
                            </a>
                            <form method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa lịch này?')">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="id" value="<?= $schedule['id'] ?>">
                                <button type="submit" class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <?php endfor; ?>
        </div>
    </div>

    <?php elseif ($action === 'add'): ?>
    <!-- Add Schedule Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Thêm lịch học mới</h3>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="add">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Lớp học</label>
                        <select name="class_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <option value="">Chọn lớp học</option>
                            <?php foreach ($classes as $class): ?>
                            <option value="<?= $class['id'] ?>"><?= htmlspecialchars($class['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Giáo viên</label>
                        <select name="teacher_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <option value="">Chọn giáo viên</option>
                            <?php foreach ($teachers as $teacher): ?>
                            <option value="<?= $teacher['id'] ?>"><?= htmlspecialchars($teacher['full_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Thứ</label>
                        <select name="day_of_week" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <?php for ($day = 1; $day <= 7; $day++): ?>
                            <option value="<?= $day ?>"><?= getVietnameseDayName($day) ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Buổi</label>
                        <select name="session" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <option value="morning">Buổi sáng</option>
                            <option value="afternoon">Buổi chiều</option>
                            <option value="evening">Buổi tối</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Giờ bắt đầu</label>
                        <input type="time" name="start_time" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Giờ kết thúc</label>
                        <input type="time" name="end_time" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Môn học</label>
                        <input type="text" name="subject" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phòng học</label>
                        <input type="text" name="room" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Tuần</label>
                        <input type="text" name="week_number" value="<?= $week_number ?>" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <a href="?page=schedules&week=<?= $week_number ?>" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Hủy
                    </a>
                    <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        Thêm lịch học
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Copy Week Modal -->
<div id="copyWeekModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Sao chép lịch tuần</h3>
            <form method="POST">
                <input type="hidden" name="action" value="copy_week">
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Từ tuần</label>
                        <select name="from_week" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <option value="">Chọn tuần nguồn</option>
                            <?php foreach ($available_weeks as $week): ?>
                            <option value="<?= $week ?>">Tuần <?= $week ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Sang tuần</label>
                        <input type="text" name="to_week" placeholder="VD: 2025-W26" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="hideCopyWeekModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Hủy
                    </button>
                    <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        Sao chép
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function changeWeek(week) {
    if (week) {
        window.location.href = '?page=schedules&week=' + week;
    }
}

function showCopyWeekModal() {
    document.getElementById('copyWeekModal').classList.remove('hidden');
}

function hideCopyWeekModal() {
    document.getElementById('copyWeekModal').classList.add('hidden');
}
</script>
