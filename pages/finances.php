<?php
/**
 * Finances Management Page
 * Vietnamese Classroom Management System
 */

// Check permission
if (!in_array($current_user['role'], ['admin', 'manager'])) {
    header('HTTP/1.0 403 Forbidden');
    die('<div class="text-center py-12"><h1 class="text-2xl font-bold text-red-600">T<PERSON>y cập bị từ chối</h1><p class="text-gray-600">Bạn không có quyền truy cập trang này.</p></div>');
}

require_once __DIR__ . '/../models/Finance.php';

$financeModel = new Finance();
$action = $_GET['action'] ?? 'list';
$transaction_type = $_GET['type'] ?? '';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $financeData = [
            'title' => $_POST['title'] ?? '',
            'amount' => $_POST['amount'] ?? 0,
            'transaction_type' => $_POST['transaction_type'] ?? 'income',
            'category' => $_POST['category'] ?? '',
            'description' => $_POST['description'] ?? '',
            'transaction_date' => $_POST['transaction_date'] ?? date('Y-m-d'),
            'creator_id' => $current_user['id'],
            'is_active' => 1
        ];
        
        if ($financeModel->create($financeData)) {
            $message = 'Thêm giao dịch thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi thêm giao dịch!';
        }
    } elseif ($action === 'edit') {
        $id = $_POST['id'] ?? 0;
        $financeData = [
            'title' => $_POST['title'] ?? '',
            'amount' => $_POST['amount'] ?? 0,
            'transaction_type' => $_POST['transaction_type'] ?? 'income',
            'category' => $_POST['category'] ?? '',
            'description' => $_POST['description'] ?? '',
            'transaction_date' => $_POST['transaction_date'] ?? date('Y-m-d'),
            'is_active' => $_POST['is_active'] ?? 1
        ];
        
        if ($financeModel->update($id, $financeData)) {
            $message = 'Cập nhật giao dịch thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi cập nhật giao dịch!';
        }
    } elseif ($action === 'delete') {
        $id = $_POST['id'] ?? 0;
        if ($financeModel->delete($id)) {
            $message = 'Xóa giao dịch thành công!';
        } else {
            $error = 'Có lỗi xảy ra khi xóa giao dịch!';
        }
        $action = 'list';
    }
}

// Get data for display
if ($action === 'list') {
    $filters = [];
    if ($transaction_type) {
        $filters['transaction_type'] = $transaction_type;
    }
    $finances = $financeModel->getAll($filters);
    $summary = $financeModel->getSummary();
} elseif ($action === 'edit') {
    $id = $_GET['id'] ?? 0;
    $finance = $financeModel->getById($id);
    if (!$finance) {
        $error = 'Không tìm thấy giao dịch!';
        $action = 'list';
        $finances = $financeModel->getAll();
        $summary = $financeModel->getSummary();
    }
}

// Get categories for dropdown
$categories = $financeModel->getCategories();
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Quản lý tài chính</h1>
        <?php if ($action === 'list'): ?>
        <a href="?page=finances&action=add" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
            <i class="fas fa-plus mr-2"></i>Thêm giao dịch
        </a>
        <?php endif; ?>
    </div>

    <!-- Summary Cards -->
    <?php if ($action === 'list' && $summary): ?>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-arrow-up text-2xl text-green-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng thu</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= formatCurrency($summary['income']['total_amount'] ?? 0) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=finances&type=income" class="font-medium text-green-600 hover:text-green-500">
                        <?= $summary['income']['transaction_count'] ?? 0 ?> giao dịch
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-arrow-down text-2xl text-red-500"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tổng chi</dt>
                            <dd class="text-lg font-medium text-gray-900"><?= formatCurrency($summary['expense']['total_amount'] ?? 0) ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=finances&type=expense" class="font-medium text-red-600 hover:text-red-500">
                        <?= $summary['expense']['transaction_count'] ?? 0 ?> giao dịch
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-balance-scale text-2xl <?= ($summary['balance'] ?? 0) >= 0 ? 'text-blue-500' : 'text-red-500' ?>"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Số dư</dt>
                            <dd class="text-lg font-medium <?= ($summary['balance'] ?? 0) >= 0 ? 'text-blue-900' : 'text-red-900' ?>">
                                <?= formatCurrency($summary['balance'] ?? 0) ?>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="?page=finances" class="font-medium text-blue-600 hover:text-blue-500">Xem tất cả</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?= htmlspecialchars($message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Filter Tabs -->
    <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <a href="?page=finances" class="<?= !$transaction_type ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Tất cả
                </a>
                <a href="?page=finances&type=income" class="<?= $transaction_type === 'income' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Thu nhập
                </a>
                <a href="?page=finances&type=expense" class="<?= $transaction_type === 'expense' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Chi phí
                </a>
            </nav>
        </div>
        
        <!-- Finances List -->
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh mục</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số tiền</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người tạo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($finances as $finance): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= formatDate($finance['transaction_date']) ?></td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($finance['title']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?= $finance['transaction_type'] === 'income' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                    <?= $finance['transaction_type'] === 'income' ? 'Thu' : 'Chi' ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($finance['category']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium <?= $finance['transaction_type'] === 'income' ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $finance['transaction_type'] === 'income' ? '+' : '-' ?><?= formatCurrency($finance['amount']) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($finance['creator_name']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="?page=finances&action=edit&id=<?= $finance['id'] ?>" class="text-primary hover:text-secondary mr-3">
                                    <i class="fas fa-edit"></i> Sửa
                                </a>
                                <form method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa giao dịch này?')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="id" value="<?= $finance['id'] ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i> Xóa
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <?= $action === 'add' ? 'Thêm giao dịch mới' : 'Chỉnh sửa giao dịch' ?>
            </h3>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="<?= $action ?>">
                <?php if ($action === 'edit'): ?>
                <input type="hidden" name="id" value="<?= $finance['id'] ?>">
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Tiêu đề</label>
                        <input type="text" name="title" value="<?= htmlspecialchars($finance['title'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Loại giao dịch</label>
                        <select name="transaction_type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <option value="income" <?= ($finance['transaction_type'] ?? '') === 'income' ? 'selected' : '' ?>>Thu nhập</option>
                            <option value="expense" <?= ($finance['transaction_type'] ?? '') === 'expense' ? 'selected' : '' ?>>Chi phí</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Số tiền (VNĐ)</label>
                        <input type="number" name="amount" value="<?= $finance['amount'] ?? '' ?>" min="0" step="1000"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Ngày giao dịch</label>
                        <input type="date" name="transaction_date" value="<?= $finance['transaction_date'] ?? date('Y-m-d') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Danh mục</label>
                        <input type="text" name="category" value="<?= htmlspecialchars($finance['category'] ?? '') ?>" 
                               list="categories" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                        <datalist id="categories">
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= htmlspecialchars($category) ?>">
                            <?php endforeach; ?>
                        </datalist>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Mô tả</label>
                    <textarea name="description" rows="3" 
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"><?= htmlspecialchars($finance['description'] ?? '') ?></textarea>
                </div>
                
                <?php if ($action === 'edit'): ?>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" <?= ($finance['is_active'] ?? 1) ? 'checked' : '' ?> 
                               class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Giao dịch hoạt động</span>
                    </label>
                </div>
                <?php endif; ?>
                
                <div class="flex justify-end space-x-3">
                    <a href="?page=finances" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Hủy
                    </a>
                    <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        <?= $action === 'add' ? 'Thêm' : 'Cập nhật' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>
