<?php
/**
 * Classes Management Page
 * Vietnamese Classroom Management System
 */

// Check permission
if (!in_array($current_user['role'], ['admin', 'manager'])) {
    header('HTTP/1.0 403 Forbidden');
    die('<div class="text-center py-12"><h1 class="text-2xl font-bold text-red-600">T<PERSON>y cập bị từ chối</h1><p class="text-gray-600">Bạn không có quyền truy cập trang này.</p></div>');
}

require_once __DIR__ . '/../models/Class.php';
require_once __DIR__ . '/../models/User.php';

$classModel = new ClassModel();
$userModel = new User();
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $classData = [
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'manager_id' => $_POST['manager_id'] ?? null,
            'is_active' => 1
        ];
        
        if ($classModel->create($classData)) {
            $message = 'Thêm lớp học thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi thêm lớp học!';
        }
    } elseif ($action === 'edit') {
        $id = $_POST['id'] ?? 0;
        $classData = [
            'name' => $_POST['name'] ?? '',
            'description' => $_POST['description'] ?? '',
            'manager_id' => $_POST['manager_id'] ?? null,
            'is_active' => $_POST['is_active'] ?? 1
        ];
        
        if ($classModel->update($id, $classData)) {
            $message = 'Cập nhật lớp học thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi cập nhật lớp học!';
        }
    } elseif ($action === 'delete') {
        $id = $_POST['id'] ?? 0;
        if ($classModel->delete($id)) {
            $message = 'Xóa lớp học thành công!';
        } else {
            $error = 'Có lỗi xảy ra khi xóa lớp học!';
        }
        $action = 'list';
    }
}

// Get data for display
if ($action === 'list') {
    if ($current_user['role'] === 'admin') {
        $classes = $classModel->getAll();
    } else {
        $classes = $classModel->getByManager($current_user['id']);
    }
} elseif ($action === 'edit') {
    $id = $_GET['id'] ?? 0;
    $class = $classModel->getById($id);
    if (!$class) {
        $error = 'Không tìm thấy lớp học!';
        $action = 'list';
        $classes = $classModel->getAll();
    }
}

// Get managers for dropdown
$managers = $userModel->getByRole('manager');
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Quản lý lớp học</h1>
        <?php if ($action === 'list'): ?>
        <a href="?page=classes&action=add" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
            <i class="fas fa-plus mr-2"></i>Thêm lớp học
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?= htmlspecialchars($message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Classes List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên lớp</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mô tả</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quản sinh</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số học sinh</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($classes as $class): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= $class['id'] ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?= htmlspecialchars($class['name']) ?></td>
                            <td class="px-6 py-4 text-sm text-gray-900"><?= htmlspecialchars($class['description']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($class['manager_name'] ?? 'Chưa phân công') ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= $class['student_count'] ?? 0 ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?= $class['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                    <?= $class['is_active'] ? 'Hoạt động' : 'Tạm khóa' ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="?page=classes&action=edit&id=<?= $class['id'] ?>" class="text-primary hover:text-secondary mr-3">
                                    <i class="fas fa-edit"></i> Sửa
                                </a>
                                <a href="?page=students&class_id=<?= $class['id'] ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-users"></i> Học sinh
                                </a>
                                <form method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa lớp học này?')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="id" value="<?= $class['id'] ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i> Xóa
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <?= $action === 'add' ? 'Thêm lớp học mới' : 'Chỉnh sửa lớp học' ?>
            </h3>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="<?= $action ?>">
                <?php if ($action === 'edit'): ?>
                <input type="hidden" name="id" value="<?= $class['id'] ?>">
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Tên lớp học</label>
                        <input type="text" name="name" value="<?= htmlspecialchars($class['name'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Quản sinh</label>
                        <select name="manager_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                            <option value="">Chọn quản sinh</option>
                            <?php foreach ($managers as $manager): ?>
                            <option value="<?= $manager['id'] ?>" <?= ($class['manager_id'] ?? '') == $manager['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($manager['full_name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Mô tả</label>
                    <textarea name="description" rows="3" 
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"><?= htmlspecialchars($class['description'] ?? '') ?></textarea>
                </div>
                
                <?php if ($action === 'edit'): ?>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" <?= ($class['is_active'] ?? 1) ? 'checked' : '' ?> 
                               class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Lớp học hoạt động</span>
                    </label>
                </div>
                <?php endif; ?>
                
                <div class="flex justify-end space-x-3">
                    <a href="?page=classes" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Hủy
                    </a>
                    <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        <?= $action === 'add' ? 'Thêm' : 'Cập nhật' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>
