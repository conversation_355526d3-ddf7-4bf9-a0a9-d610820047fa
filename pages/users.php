<?php
/**
 * Users Management Page
 * Vietnamese Classroom Management System
 */

// Check admin permission
if ($current_user['role'] !== 'admin') {
    header('HTTP/1.0 403 Forbidden');
    die('<div class="text-center py-12"><h1 class="text-2xl font-bold text-red-600">T<PERSON>y cập bị từ chối</h1><p class="text-gray-600">Bạn không có quyền truy cập trang này.</p></div>');
}

require_once __DIR__ . '/../models/User.php';

$userModel = new User();
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $userData = [
            'username' => $_POST['username'] ?? '',
            'email' => $_POST['email'] ?? '',
            'password_hash' => password_hash($_POST['password'] ?? '', PASSWORD_DEFAULT),
            'full_name' => $_POST['full_name'] ?? '',
            'phone' => $_POST['phone'] ?? '',
            'role' => $_POST['role'] ?? 'user',
            'is_active' => 1
        ];
        
        if ($userModel->create($userData)) {
            $message = 'Thêm người dùng thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi thêm người dùng!';
        }
    } elseif ($action === 'edit') {
        $id = $_POST['id'] ?? 0;
        $userData = [
            'username' => $_POST['username'] ?? '',
            'email' => $_POST['email'] ?? '',
            'full_name' => $_POST['full_name'] ?? '',
            'phone' => $_POST['phone'] ?? '',
            'role' => $_POST['role'] ?? 'user',
            'is_active' => $_POST['is_active'] ?? 1
        ];
        
        if (!empty($_POST['password'])) {
            $userData['password_hash'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
        }
        
        if ($userModel->update($id, $userData)) {
            $message = 'Cập nhật người dùng thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi cập nhật người dùng!';
        }
    } elseif ($action === 'delete') {
        $id = $_POST['id'] ?? 0;
        if ($userModel->delete($id)) {
            $message = 'Xóa người dùng thành công!';
        } else {
            $error = 'Có lỗi xảy ra khi xóa người dùng!';
        }
        $action = 'list';
    }
}

// Get data for display
if ($action === 'list') {
    $users = $userModel->getAll();
} elseif ($action === 'edit') {
    $id = $_GET['id'] ?? 0;
    $user = $userModel->getById($id);
    if (!$user) {
        $error = 'Không tìm thấy người dùng!';
        $action = 'list';
        $users = $userModel->getAll();
    }
}
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Quản lý người dùng</h1>
        <?php if ($action === 'list'): ?>
        <a href="?page=users&action=add" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
            <i class="fas fa-plus mr-2"></i>Thêm người dùng
        </a>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?= htmlspecialchars($message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Users List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên đăng nhập</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Họ tên</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vai trò</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= $user['id'] ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($user['username']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($user['full_name']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($user['email']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?= $user['role'] === 'admin' ? 'bg-red-100 text-red-800' : 
                                        ($user['role'] === 'manager' ? 'bg-blue-100 text-blue-800' : 
                                        ($user['role'] === 'teacher' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')) ?>">
                                    <?= getRoleDisplay($user['role']) ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?= $user['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                    <?= $user['is_active'] ? 'Hoạt động' : 'Tạm khóa' ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="?page=users&action=edit&id=<?= $user['id'] ?>" class="text-primary hover:text-secondary mr-3">
                                    <i class="fas fa-edit"></i> Sửa
                                </a>
                                <?php if ($user['id'] != $current_user['id']): ?>
                                <form method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa người dùng này?')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="id" value="<?= $user['id'] ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i> Xóa
                                    </button>
                                </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <?= $action === 'add' ? 'Thêm người dùng mới' : 'Chỉnh sửa người dùng' ?>
            </h3>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="<?= $action ?>">
                <?php if ($action === 'edit'): ?>
                <input type="hidden" name="id" value="<?= $user['id'] ?>">
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Tên đăng nhập</label>
                        <input type="text" name="username" value="<?= htmlspecialchars($user['username'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" value="<?= htmlspecialchars($user['email'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Họ tên</label>
                        <input type="text" name="full_name" value="<?= htmlspecialchars($user['full_name'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Số điện thoại</label>
                        <input type="text" name="phone" value="<?= htmlspecialchars($user['phone'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Vai trò</label>
                        <select name="role" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                            <option value="user" <?= ($user['role'] ?? '') === 'user' ? 'selected' : '' ?>>Người dùng</option>
                            <option value="teacher" <?= ($user['role'] ?? '') === 'teacher' ? 'selected' : '' ?>>Giáo viên</option>
                            <option value="manager" <?= ($user['role'] ?? '') === 'manager' ? 'selected' : '' ?>>Quản sinh</option>
                            <option value="admin" <?= ($user['role'] ?? '') === 'admin' ? 'selected' : '' ?>>Quản trị viên</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Mật khẩu <?= $action === 'edit' ? '(để trống nếu không đổi)' : '' ?></label>
                        <input type="password" name="password" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" 
                               <?= $action === 'add' ? 'required' : '' ?>>
                    </div>
                </div>
                
                <?php if ($action === 'edit'): ?>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" <?= ($user['is_active'] ?? 1) ? 'checked' : '' ?> 
                               class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Tài khoản hoạt động</span>
                    </label>
                </div>
                <?php endif; ?>
                
                <div class="flex justify-end space-x-3">
                    <a href="?page=users" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Hủy
                    </a>
                    <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        <?= $action === 'add' ? 'Thêm' : 'Cập nhật' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>
