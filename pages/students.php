<?php
/**
 * Students Management Page
 * Vietnamese Classroom Management System
 */

// Check permission
if (!in_array($current_user['role'], ['admin', 'manager', 'teacher'])) {
    header('HTTP/1.0 403 Forbidden');
    die('<div class="text-center py-12"><h1 class="text-2xl font-bold text-red-600">T<PERSON>y cập bị từ chối</h1><p class="text-gray-600">Bạn không có quyền truy cập trang này.</p></div>');
}

require_once __DIR__ . '/../models/Student.php';
require_once __DIR__ . '/../models/Class.php';

$studentModel = new Student();
$classModel = new ClassModel();
$action = $_GET['action'] ?? 'list';
$class_id = $_GET['class_id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add') {
        $studentData = [
            'full_name' => $_POST['full_name'] ?? '',
            'date_of_birth' => $_POST['date_of_birth'] ?? null,
            'address' => $_POST['address'] ?? '',
            'parent_name' => $_POST['parent_name'] ?? '',
            'parent_phone' => $_POST['parent_phone'] ?? '',
            'profile_url' => $_POST['profile_url'] ?? '',
            'class_id' => $_POST['class_id'] ?? null,
            'is_active' => 1
        ];
        
        if ($studentModel->create($studentData)) {
            $message = 'Thêm học sinh thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi thêm học sinh!';
        }
    } elseif ($action === 'edit') {
        $id = $_POST['id'] ?? 0;
        $studentData = [
            'full_name' => $_POST['full_name'] ?? '',
            'date_of_birth' => $_POST['date_of_birth'] ?? null,
            'address' => $_POST['address'] ?? '',
            'parent_name' => $_POST['parent_name'] ?? '',
            'parent_phone' => $_POST['parent_phone'] ?? '',
            'profile_url' => $_POST['profile_url'] ?? '',
            'class_id' => $_POST['class_id'] ?? null,
            'is_active' => $_POST['is_active'] ?? 1
        ];
        
        if ($studentModel->update($id, $studentData)) {
            $message = 'Cập nhật học sinh thành công!';
            $action = 'list';
        } else {
            $error = 'Có lỗi xảy ra khi cập nhật học sinh!';
        }
    } elseif ($action === 'delete') {
        $id = $_POST['id'] ?? 0;
        if ($studentModel->delete($id)) {
            $message = 'Xóa học sinh thành công!';
        } else {
            $error = 'Có lỗi xảy ra khi xóa học sinh!';
        }
        $action = 'list';
    }
}

// Get data for display
if ($action === 'list') {
    if ($class_id) {
        $students = $studentModel->getByClass($class_id);
        $class = $classModel->getById($class_id);
    } else {
        $students = $studentModel->getAll();
    }
} elseif ($action === 'edit') {
    $id = $_GET['id'] ?? 0;
    $student = $studentModel->getById($id);
    if (!$student) {
        $error = 'Không tìm thấy học sinh!';
        $action = 'list';
        $students = $studentModel->getAll();
    }
}

// Get classes for dropdown
$classes = $classModel->getAll();
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Quản lý học sinh</h1>
            <?php if ($class_id && isset($class)): ?>
            <p class="text-gray-600">Lớp: <?= htmlspecialchars($class['name']) ?></p>
            <?php endif; ?>
        </div>
        <?php if ($action === 'list' && in_array($current_user['role'], ['admin', 'manager'])): ?>
        <div class="flex space-x-2">
            <?php if ($class_id): ?>
            <a href="?page=students" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Tất cả học sinh
            </a>
            <?php endif; ?>
            <a href="?page=students&action=add<?= $class_id ? '&class_id=' . $class_id : '' ?>" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                <i class="fas fa-plus mr-2"></i>Thêm học sinh
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <?= htmlspecialchars($message) ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <?= htmlspecialchars($error) ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Students List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã HS</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Họ tên</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày sinh</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lớp</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phụ huynh</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SĐT</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                            <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($students as $student): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?= htmlspecialchars($student['student_code']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($student['full_name']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= formatDate($student['date_of_birth']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($student['class_name'] ?? 'Chưa phân lớp') ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($student['parent_name']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($student['parent_phone']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?= $student['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                    <?= $student['is_active'] ? 'Hoạt động' : 'Tạm khóa' ?>
                                </span>
                            </td>
                            <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="?page=students&action=edit&id=<?= $student['id'] ?>" class="text-primary hover:text-secondary mr-3">
                                    <i class="fas fa-edit"></i> Sửa
                                </a>
                                <form method="POST" class="inline" onsubmit="return confirm('Bạn có chắc muốn xóa học sinh này?')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="id" value="<?= $student['id'] ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i> Xóa
                                    </button>
                                </form>
                            </td>
                            <?php endif; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <?= $action === 'add' ? 'Thêm học sinh mới' : 'Chỉnh sửa học sinh' ?>
            </h3>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="<?= $action ?>">
                <?php if ($action === 'edit'): ?>
                <input type="hidden" name="id" value="<?= $student['id'] ?>">
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Họ tên học sinh</label>
                        <input type="text" name="full_name" value="<?= htmlspecialchars($student['full_name'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Ngày sinh</label>
                        <input type="date" name="date_of_birth" value="<?= $student['date_of_birth'] ?? '' ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Tên phụ huynh</label>
                        <input type="text" name="parent_name" value="<?= htmlspecialchars($student['parent_name'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Số điện thoại</label>
                        <input type="text" name="parent_phone" value="<?= htmlspecialchars($student['parent_phone'] ?? '') ?>" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Lớp học</label>
                        <select name="class_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                            <option value="">Chọn lớp học</option>
                            <?php foreach ($classes as $class): ?>
                            <option value="<?= $class['id'] ?>" <?= ($student['class_id'] ?? $class_id) == $class['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($class['name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Địa chỉ</label>
                    <textarea name="address" rows="2" 
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"><?= htmlspecialchars($student['address'] ?? '') ?></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">URL ảnh đại diện</label>
                    <input type="url" name="profile_url" value="<?= htmlspecialchars($student['profile_url'] ?? '') ?>" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                </div>
                
                <?php if ($action === 'edit'): ?>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" <?= ($student['is_active'] ?? 1) ? 'checked' : '' ?> 
                               class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Học sinh hoạt động</span>
                    </label>
                </div>
                <?php endif; ?>
                
                <div class="flex justify-end space-x-3">
                    <a href="?page=students<?= $class_id ? '&class_id=' . $class_id : '' ?>" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Hủy
                    </a>
                    <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        <?= $action === 'add' ? 'Thêm' : 'Cập nhật' ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>
</div>
