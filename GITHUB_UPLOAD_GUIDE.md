# 📚 Hướng dẫn đẩy dự án Vietnamese Classroom Management lên GitHub

## 🎯 **Tổng quan**

Hướng dẫn từng bước để upload dự án từ máy tính local lên GitHub repository.

---

## 🔧 **Bước 1: Chuẩn bị dự án**

### **1.1. <PERSON><PERSON><PERSON> tra cấu trúc dự án**

Đ<PERSON>m bảo dự án có cấu trúc như sau:
```
qlht/
├── app/
│   ├── __init__.py
│   ├── models/
│   ├── routes/
│   ├── templates/
│   └── static/
├── migrations/
├── config.py
├── requirements.txt
├── run.py
├── app.py
├── README.md
└── .env.example
```

### **1.2. Tạo .gitignore**

```bash
# Tạo file .gitignore
touch .gitignore
```

Nội dung .gitignore:
```gitignore
# Environment variables
.env
.env.local
.env.production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.venv/

# Database
*.db
*.sqlite
*.sqlite3
app.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Flask
instance/
.webassets-cache

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Pytest
.pytest_cache/

# Migration files (optional - có thể giữ hoặc ignore)
# migrations/versions/

# Uploads
uploads/
static/uploads/
```

### **1.3. Tạo .env.example**

```bash
# Environment variables for production deployment
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db
FLASK_ENV=production
FLASK_DEBUG=False

# For PostgreSQL (production)
# DATABASE_URL=postgresql://username:password@hostname:port/database_name

# For MongoDB (if using)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name

# Optional settings
PORT=5000
```

---

## 🌐 **Bước 2: Tạo GitHub Repository**

### **2.1. Tạo repository trên GitHub**

1. **Đăng nhập GitHub**: https://github.com
2. **Click "New repository"** (nút xanh lá)
3. **Điền thông tin**:
   ```
   Repository name: vietnamese-classroom-management
   Description: Hệ thống quản lý lớp học tiếng Việt với Flask và Tailwind CSS
   Visibility: Public (hoặc Private nếu muốn)
   ☐ Add a README file (không tick - vì đã có)
   ☐ Add .gitignore (không tick - vì đã tạo)
   ☐ Choose a license (có thể chọn MIT License)
   ```
4. **Click "Create repository"**

### **2.2. Lưu lại thông tin repository**

GitHub sẽ hiển thị URL như:
```
https://github.com/[username]/vietnamese-classroom-management.git
```

---

## 💻 **Bước 3: Cài đặt Git (nếu chưa có)**

### **3.1. Kiểm tra Git**

```bash
git --version
```

### **3.2. Cài đặt Git (nếu chưa có)**

**macOS:**
```bash
brew install git
```

**Windows:**
- Download từ: https://git-scm.com/download/windows

**Ubuntu/Debian:**
```bash
sudo apt-get install git
```

### **3.3. Cấu hình Git**

```bash
git config --global user.name "Tên của bạn"
git config --global user.email "<EMAIL>"
```

---

## 🚀 **Bước 4: Upload dự án lên GitHub**

### **4.1. Mở Terminal/Command Prompt**

```bash
# Di chuyển vào thư mục dự án
cd /path/to/your/qlht
# Hoặc trên Windows: cd C:\path\to\your\qlht
```

### **4.2. Khởi tạo Git repository**

```bash
# Khởi tạo git repository
git init

# Kiểm tra status
git status
```

### **4.3. Thêm remote repository**

```bash
# Thêm remote origin (thay [username] bằng username GitHub của bạn)
git remote add origin https://github.com/[username]/vietnamese-classroom-management.git

# Kiểm tra remote
git remote -v
```

### **4.4. Add và commit files**

```bash
# Thêm tất cả files
git add .

# Kiểm tra files sẽ được commit
git status

# Commit với message
git commit -m "Initial commit: Vietnamese Classroom Management System

- Complete Flask application with role-based access control
- Admin, Manager, Teacher roles with appropriate permissions
- Class management, student tracking, attendance system
- Weekly-based scheduling with conflict detection
- Financial management and expense tracking
- Responsive UI with Tailwind CSS
- PostgreSQL/SQLite database support
- Ready for production deployment"
```

### **4.5. Push lên GitHub**

```bash
# Push lên GitHub (lần đầu)
git push -u origin main

# Hoặc nếu branch mặc định là master
git push -u origin master
```

**Lưu ý**: Nếu gặp lỗi authentication, bạn cần:
1. **Personal Access Token**: Tạo token tại GitHub Settings > Developer settings > Personal access tokens
2. **SSH Key**: Hoặc setup SSH key cho GitHub

---

## 🔐 **Bước 5: Authentication (nếu cần)**

### **5.1. Sử dụng Personal Access Token**

1. **Tạo token**:
   - GitHub > Settings > Developer settings > Personal access tokens > Tokens (classic)
   - Generate new token > Select scopes: `repo`, `workflow`
   - Copy token (chỉ hiển thị 1 lần)

2. **Sử dụng token**:
   ```bash
   # Khi git push hỏi password, dùng token thay vì password
   Username: [github-username]
   Password: [personal-access-token]
   ```

### **5.2. Hoặc sử dụng SSH Key**

```bash
# Tạo SSH key
ssh-keygen -t ed25519 -C "<EMAIL>"

# Copy public key
cat ~/.ssh/id_ed25519.pub

# Thêm vào GitHub: Settings > SSH and GPG keys > New SSH key

# Thay đổi remote URL sang SSH
git remote set-<NAME_EMAIL>:[username]/vietnamese-classroom-management.git
```

---

## 📝 **Bước 6: Cập nhật README.md**

### **6.1. Cập nhật thông tin repository**

```markdown
# 🎓 Vietnamese Classroom Management System

[![GitHub](https://img.shields.io/github/license/[username]/vietnamese-classroom-management)](https://github.com/[username]/vietnamese-classroom-management/blob/main/LICENSE)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Flask](https://img.shields.io/badge/flask-2.3+-green.svg)](https://flask.palletsprojects.com/)

Hệ thống quản lý lớp học tiếng Việt hoàn chỉnh với Flask và Tailwind CSS.

## 🌟 **Live Demo**

- **Demo URL**: https://your-app.onrender.com
- **GitHub**: https://github.com/[username]/vietnamese-classroom-management

## 🚀 **Quick Start**

```bash
# Clone repository
git clone https://github.com/[username]/vietnamese-classroom-management.git
cd vietnamese-classroom-management

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env

# Initialize database
flask db upgrade

# Run application
python run.py
```

## 👤 **Demo Accounts**

| Role | Username | Password |
|------|----------|----------|
| Admin | admin | admin123 |
| Manager | manager | manager123 |
| Teacher | teacher | teacher123 |

[... rest of README content ...]
```

---

## 🔄 **Bước 7: Workflow cho updates sau này**

### **7.1. Workflow hàng ngày**

```bash
# Kiểm tra status
git status

# Add changes
git add .

# Commit với message mô tả
git commit -m "Fix: Resolve schedule conflict detection for weekly schedules

- Add week_number filter to conflict checking logic
- Update assignment creation to include week_number
- Improve error messages with specific week information"

# Push lên GitHub
git push
```

### **7.2. Tạo branches cho features mới**

```bash
# Tạo branch mới
git checkout -b feature/mongodb-integration

# Làm việc trên branch...
git add .
git commit -m "Add MongoDB integration support"

# Push branch
git push -u origin feature/mongodb-integration

# Merge về main (sau khi test)
git checkout main
git merge feature/mongodb-integration
git push
```

---

## 🎯 **Bước 8: Deployment từ GitHub**

### **8.1. Auto-deploy với Render**

1. **Connect GitHub**: Render Dashboard > New Web Service > Connect GitHub
2. **Select Repository**: vietnamese-classroom-management
3. **Auto-deploy**: Mỗi khi push lên main branch sẽ tự động deploy

### **8.2. Auto-deploy với Heroku**

```bash
# Connect Heroku với GitHub
heroku git:remote -a your-app-name

# Auto deploy từ GitHub
# Heroku Dashboard > Deploy > GitHub > Enable Automatic Deploys
```

---

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **Permission denied (publickey)**:
   ```bash
   # Setup SSH key hoặc dùng HTTPS với token
   git remote set-url origin https://github.com/[username]/vietnamese-classroom-management.git
   ```

2. **Large files error**:
   ```bash
   # Remove large files từ git history
   git rm --cached large-file.db
   echo "*.db" >> .gitignore
   git add .gitignore
   git commit -m "Remove database files from tracking"
   ```

3. **Merge conflicts**:
   ```bash
   # Pull latest changes trước khi push
   git pull origin main
   # Resolve conflicts manually
   git add .
   git commit -m "Resolve merge conflicts"
   git push
   ```

---

## 📞 **Support**

Nếu gặp vấn đề:
1. Check GitHub repository settings
2. Verify git remote configuration: `git remote -v`
3. Check authentication: Personal Access Token hoặc SSH key
4. Review .gitignore để đảm bảo không push sensitive files

**🎉 Chúc mừng! Dự án đã được upload lên GitHub thành công!**

**Repository URL**: https://github.com/[username]/vietnamese-classroom-management
