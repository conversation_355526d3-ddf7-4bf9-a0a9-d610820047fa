# 🚀 Deployment Guide - Vietnamese Classroom Management System

## 📋 Pre-deployment Checklist

✅ All PHP files syntax checked
✅ Database schema validated  
✅ File structure verified
✅ Configuration files ready

## 🛠️ Deployment Steps

### 1. Server Requirements
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Web server (Apache/Nginx)
- mod_rewrite enabled (for Apache)

### 2. Upload Files
```bash
# Upload all files to your web server
# Maintain directory structure
```

### 3. Database Setup
```sql
-- Create database
CREATE DATABASE vietnamese_classroom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
mysql -u username -p vietnamese_classroom < database/schema.sql
```

### 4. Configuration
```php
// config/database.php
$_ENV['DB_HOST'] = 'your_host';
$_ENV['DB_NAME'] = 'vietnamese_classroom';
$_ENV['DB_USER'] = 'your_username';
$_ENV['DB_PASS'] = 'your_password';
```

### 5. File Permissions
```bash
chmod 755 index.php
chmod -R 755 config/
chmod -R 755 includes/
chmod -R 755 models/
chmod -R 755 pages/
chmod -R 755 auth/
```

### 6. Test Installation
- Access: http://yourdomain.com/test_system.php
- Login: http://yourdomain.com/
- Default credentials: admin / admin123

## 🔧 Troubleshooting

### Common Issues:
1. **Database Connection Failed**
   - Check database credentials
   - Verify database exists
   - Check MySQL service status

2. **Permission Denied**
   - Set correct file permissions
   - Check web server user

3. **PHP Errors**
   - Enable error reporting
   - Check PHP version
   - Verify required extensions

### Required PHP Extensions:
- PDO
- PDO_MySQL
- mbstring
- json
- session

## 📞 Support
- Website: https://qllhttbb.vn/
- Email: <EMAIL>

---
Generated by deployment checker
