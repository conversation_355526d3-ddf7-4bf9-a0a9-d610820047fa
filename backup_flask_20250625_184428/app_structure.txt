app/
  .DS_Store
  __init__.py
  forms/
    event_forms.py
    class_forms.py
    schedule_forms.py
    user_forms.py
    time_slot_forms.py
    financial_forms.py
    __init__.py
    finance_forms.py
    expense_forms.py
    auth_forms.py
    __pycache__/
      auth_forms.cpython-313.pyc
      class_forms.cpython-313.pyc
      financial_forms.cpython-313.pyc
      time_slot_forms.cpython-313.pyc
      finance_forms.cpython-313.pyc
      user_forms.cpython-313.pyc
      expense_forms.cpython-313.pyc
      __init__.cpython-313.pyc
      event_forms.cpython-313.pyc
      schedule_forms.cpython-313.pyc
  auth/
  utils/
    excel_export.py
    __pycache__/
      excel_export.cpython-313.pyc
  models/
    user.py
    class_model.py
    event.py
    expense.py
    finance.py
    attendance.py
    student_schedule.py
    __init__.py
    mongo_models.py
    schedule.py
    student.py
    financial_transaction.py
    time_slot.py
    __pycache__/
      schedule.cpython-313.pyc
      student_schedule.cpython-313.pyc
      class_model.cpython-313.pyc
      attendance.cpython-313.pyc
      student.cpython-313.pyc
      finance.cpython-313.pyc
      user.cpython-313.pyc
      mongo_models.cpython-313.pyc
      financial_transaction.cpython-313.pyc
      time_slot.cpython-313.pyc
      __init__.cpython-313.pyc
      expense.cpython-313.pyc
      event.cpython-313.pyc
  __pycache__/
    __init__.cpython-313.pyc
  static/
    css/
      custom.css
      style.css
    js/
      main.js
  templates/
    base_tailwind.html
    base.html
    mongo_test.html
    dashboard.html
    dashboard_tailwind.html
    expense/
      expenses_tailwind.html
      dashboard_tailwind.html
      create_expense_tailwind.html
    calendar/
      schedule_detail_modal_tailwind.html
      month_view.html
      calendar.html
      day_view.html
      month_view_tailwind.html
      day_view_tailwind.html
      schedule_detail_modal.html
      calendar_tailwind.html
    financial/
      create_transaction_tailwind.html
      donations_tailwind.html
      distribute_donation_tailwind.html
      dashboard_tailwind.html
      create_donation_tailwind.html
      transactions_tailwind.html
    auth/
      register.html
      login.html
      login_tailwind.html
      profile_tailwind.html
    admin/
      edit_user_tailwind.html
      create_class.html
      users.html
      create_user.html
      users_tailwind.html
      classes.html
      classes_tailwind.html
      create_user_tailwind.html
    user/
      dashboard_tailwind.html
    manager/
      edit_class_tailwind.html
      add_students_to_schedule_tailwind.html
      add_student_to_class_wtf_tailwind.html
      class_details_modal_tailwind.html
      add_student_to_class_simple_tailwind.html
      students.html
      add_student_to_class_no_csrf_tailwind.html
      create_student_tailwind.html
      notification_generator_tailwind.html
      schedule_assignment_tailwind.html
      copy_schedule_tailwind.html
      schedule.html
      class_details_tailwind.html
      edit_student_tailwind.html
      schedule_tailwind.html
      edit_student.html
      edit_assignment_tailwind.html
      time_slots.html
      schedule_form_tailwind.html
      students_tailwind.html
      time_slots_tailwind.html
      add_student_to_class_tailwind.html
      classes.html
      classes_tailwind.html
      copy_schedule_new_tailwind.html
      create_student.html
      add_time_slot.html
      attendance_tailwind.html
      create_class_tailwind.html
    finance/
      dashboard.html
      add_transaction.html
    teacher/
      schedule.html
      attendance.html
      attendance_tailwind.html
  routes/
    auth.py
    user.py
    expense.py
    finance.py
    teacher.py
    __init__.py
    financial.py
    mongo_test.py
    api.py
    calendar.py
    admin.py
    main.py
    manager.py
    __pycache__/
      main.cpython-313.pyc
      manager.cpython-313.pyc
      api.cpython-313.pyc
      teacher.cpython-313.pyc
      mongo_test.cpython-313.pyc
      finance.cpython-313.pyc
      user.cpython-313.pyc
      calendar.cpython-313.pyc
      auth.cpython-313.pyc
      admin.cpython-313.pyc
      financial.cpython-313.pyc
      __init__.cpython-313.pyc
      expense.cpython-313.pyc
  services/
