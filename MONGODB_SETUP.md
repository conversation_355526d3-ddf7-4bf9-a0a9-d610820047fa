# 🍃 Hướng dẫn kết nối MongoDB cho Vietnamese Classroom Management System

## 📋 **Tổng quan**

Hướng dẫn này sẽ giúp bạn chuyển đổi từ PostgreSQL/SQLite sang MongoDB cho hệ thống quản lý lớp học.

---

## 🔧 **Bước 1: Cài đặt dependencies**

### **1.1. Cập nhật requirements.txt**

Thêm các package MongoDB vào `requirements.txt`:

```txt
# MongoDB dependencies
pymongo==4.6.1
mongoengine==0.27.0
flask-mongoengine==1.0.0

# Existing dependencies...
Flask==2.3.3
Flask-Login==0.6.3
# ... (giữ nguyên các dependencies khác)
```

### **1.2. Cài đặt packages**

```bash
pip install pymongo mongoengine flask-mongoengine
```

---

## 🗄️ **Bước 2: Setup MongoDB**

### **2.1. MongoDB Atlas (Cloud - Khuyến nghị)**

1. **Tạo tài khoản**: https://www.mongodb.com/atlas
2. **Tạo cluster mới**:
   - Chọn **FREE tier** (M0)
   - Region: **Singapore** (gần Việt Nam nhất)
   - Cluster Name: `vietnamese-classroom`

3. **Tạo Database User**:
   - Username: `classroom_admin`
   - Password: `[tạo password mạnh]`
   - Role: `Atlas admin`

4. **Whitelist IP**:
   - Add IP: `0.0.0.0/0` (cho phép tất cả IP - chỉ dùng cho development)
   - Hoặc add IP cụ thể của server

5. **Lấy Connection String**:
   ```
   mongodb+srv://classroom_admin:<password>@vietnamese-classroom.xxxxx.mongodb.net/vietnamese_classroom?retryWrites=true&w=majority
   ```

### **2.2. MongoDB Local (Development)**

```bash
# macOS
brew install mongodb-community

# Ubuntu/Debian
sudo apt-get install mongodb

# Windows
# Download từ https://www.mongodb.com/try/download/community

# Start MongoDB
mongod --dbpath /path/to/data/directory
```

---

## ⚙️ **Bước 3: Cấu hình Flask App**

### **3.1. Cập nhật config.py**

```python
import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # MongoDB Configuration
    MONGODB_SETTINGS = {
        'host': os.environ.get('MONGODB_URI') or 'mongodb://localhost:27017/vietnamese_classroom',
        'connect': False,  # Disable connect on import
    }
    
    # Legacy SQL Database (for migration period)
    database_url = os.environ.get('DATABASE_URL')
    if database_url:
        if database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://', 1)
    
    SQLALCHEMY_DATABASE_URI = database_url or 'sqlite:///' + os.path.join(basedir, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Other settings...
    WTF_CSRF_ENABLED = False
    POSTS_PER_PAGE = 25
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024
    PORT = int(os.environ.get('PORT', 5000))
```

### **3.2. Cập nhật app/__init__.py**

```python
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from flask_mongoengine import MongoEngine
from config import Config

# Initialize extensions
db = SQLAlchemy()  # Keep for migration
mongo = MongoEngine()  # New MongoDB
migrate = Migrate()
login = LoginManager()
csrf = CSRFProtect()

login.login_view = 'auth.login'
login.login_message = 'Vui lòng đăng nhập để truy cập trang này.'

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)  # Keep for migration period
    mongo.init_app(app)  # New MongoDB
    migrate.init_app(app, db)
    login.init_app(app)
    csrf.init_app(app)

    # User loader for Flask-Login
    @login.user_loader
    def load_user(user_id):
        # Try MongoDB first, fallback to SQL
        try:
            from app.models.mongo_models import MongoUser
            return MongoUser.objects(id=user_id).first()
        except:
            from app.models.user import User
            return User.query.get(int(user_id))

    # Register blueprints
    from app.routes import main, auth, admin, manager, teacher, user, finance, calendar, expense, financial, api
    app.register_blueprint(main.bp)
    app.register_blueprint(auth.bp, url_prefix='/auth')
    app.register_blueprint(admin.bp, url_prefix='/admin')
    app.register_blueprint(manager.bp, url_prefix='/manager')
    app.register_blueprint(teacher.bp, url_prefix='/teacher')
    app.register_blueprint(user.bp, url_prefix='/user')
    app.register_blueprint(finance.bp, url_prefix='/finance')
    app.register_blueprint(calendar.bp, url_prefix='/calendar')
    app.register_blueprint(expense.bp, url_prefix='/expense')
    app.register_blueprint(financial.bp)
    app.register_blueprint(api.bp)

    return app

from app import models
```

---

## 📊 **Bước 4: Tạo MongoDB Models**

### **4.1. Tạo file app/models/mongo_models.py**

```python
from datetime import datetime
from mongoengine import Document, StringField, DateTimeField, BooleanField, IntField, ReferenceField, ListField, EmbeddedDocument, EmbeddedDocumentField
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

class MongoUser(Document, UserMixin):
    """MongoDB User Model"""
    username = StringField(max_length=64, required=True, unique=True)
    email = StringField(max_length=120, required=True, unique=True)
    password_hash = StringField(max_length=128)
    full_name = StringField(max_length=100, required=True)
    phone = StringField(max_length=20)
    role = StringField(max_length=20, required=True, choices=['admin', 'manager', 'teacher', 'user'])
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'users',
        'indexes': ['username', 'email', 'role']
    }
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'
    
    def is_manager(self):
        return self.role == 'manager'
    
    def is_teacher(self):
        return self.role == 'teacher'
    
    def is_user(self):
        return self.role == 'user'

class MongoClass(Document):
    """MongoDB Class Model"""
    name = StringField(max_length=50, required=True)
    description = StringField()
    manager = ReferenceField(MongoUser)
    teachers = ListField(ReferenceField(MongoUser))
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'classes',
        'indexes': ['name', 'manager', 'is_active']
    }

class MongoStudent(Document):
    """MongoDB Student Model"""
    student_code = StringField(max_length=20, required=True, unique=True)
    full_name = StringField(max_length=100, required=True)
    date_of_birth = DateTimeField()
    address = StringField()
    parent_name = StringField(max_length=100)
    parent_phone = StringField(max_length=20)
    profile_url = StringField(max_length=500)
    class_ref = ReferenceField(MongoClass)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'students',
        'indexes': ['student_code', 'full_name', 'class_ref']
    }

class MongoSchedule(Document):
    """MongoDB Schedule Model"""
    class_ref = ReferenceField(MongoClass, required=True)
    teacher = ReferenceField(MongoUser, required=True)
    day_of_week = IntField(required=True, min_value=1, max_value=7)
    session = StringField(max_length=20, required=True, choices=['morning', 'afternoon', 'evening'])
    start_time = StringField(required=True)  # Store as "HH:MM" format
    end_time = StringField(required=True)
    subject = StringField(max_length=100)
    room = StringField(max_length=50)
    week_number = StringField(max_length=10, required=True)  # Format: 2025-W25
    week_created = StringField(max_length=10)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'schedules',
        'indexes': [
            ('week_number', 'day_of_week', 'teacher'),
            ('class_ref', 'week_number'),
            'teacher',
            'is_active'
        ]
    }
    
    @staticmethod
    def get_current_week():
        """Get current week in YYYY-WXX format"""
        now = datetime.now()
        year, week, _ = now.isocalendar()
        return f"{year}-W{week:02d}"

class MongoAttendance(Document):
    """MongoDB Attendance Model"""
    student = ReferenceField(MongoStudent, required=True)
    schedule = ReferenceField(MongoSchedule, required=True)
    date = DateTimeField(required=True)
    status = StringField(max_length=20, required=True, choices=['present', 'absent', 'late', 'excused'])
    notes = StringField()
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'attendance',
        'indexes': [
            ('student', 'schedule', 'date'),
            'date',
            'status'
        ]
    }
```

---

## 🔄 **Bước 5: Migration Script**

### **5.1. Tạo script migration từ SQL sang MongoDB**

```python
# migration_to_mongo.py
from app import create_app, db
from app.models.user import User
from app.models.class_model import Class
from app.models.student import Student
from app.models.schedule import Schedule
from app.models.attendance import Attendance
from app.models.mongo_models import MongoUser, MongoClass, MongoStudent, MongoSchedule, MongoAttendance
from datetime import datetime

def migrate_users():
    """Migrate users from SQL to MongoDB"""
    print("Migrating users...")
    sql_users = User.query.all()
    
    for sql_user in sql_users:
        mongo_user = MongoUser(
            username=sql_user.username,
            email=sql_user.email,
            password_hash=sql_user.password_hash,
            full_name=sql_user.full_name,
            phone=sql_user.phone,
            role=sql_user.role,
            is_active=sql_user.is_active,
            created_at=sql_user.created_at or datetime.utcnow()
        )
        mongo_user.save()
        print(f"Migrated user: {sql_user.username}")

def migrate_classes():
    """Migrate classes from SQL to MongoDB"""
    print("Migrating classes...")
    sql_classes = Class.query.all()
    
    for sql_class in sql_classes:
        # Find manager in MongoDB
        manager = MongoUser.objects(username=sql_class.manager.username).first() if sql_class.manager else None
        
        mongo_class = MongoClass(
            name=sql_class.name,
            description=sql_class.description,
            manager=manager,
            is_active=sql_class.is_active,
            created_at=sql_class.created_at or datetime.utcnow()
        )
        mongo_class.save()
        print(f"Migrated class: {sql_class.name}")

def run_migration():
    """Run full migration"""
    app = create_app()
    
    with app.app_context():
        print("Starting migration from SQL to MongoDB...")
        
        # Clear existing MongoDB data
        MongoUser.drop_collection()
        MongoClass.drop_collection()
        MongoStudent.drop_collection()
        MongoSchedule.drop_collection()
        MongoAttendance.drop_collection()
        
        # Run migrations
        migrate_users()
        migrate_classes()
        # Add more migration functions as needed
        
        print("Migration completed!")

if __name__ == '__main__':
    run_migration()
```

---

## 🌍 **Bước 6: Environment Variables**

### **6.1. Cập nhật .env**

```bash
# MongoDB Configuration
MONGODB_URI=mongodb+srv://classroom_admin:<password>@vietnamese-classroom.xxxxx.mongodb.net/vietnamese_classroom?retryWrites=true&w=majority

# Legacy SQL Database (keep during migration)
DATABASE_URL=postgresql://username:password@hostname:port/database

# Other settings
SECRET_KEY=your-secret-key-here
FLASK_ENV=production
FLASK_DEBUG=False
```

---

## 🚀 **Bước 7: Testing**

### **7.1. Test MongoDB connection**

```python
# test_mongo.py
from app import create_app
from app.models.mongo_models import MongoUser

app = create_app()

with app.app_context():
    try:
        # Test connection
        user_count = MongoUser.objects.count()
        print(f"✅ MongoDB connected! Users count: {user_count}")
        
        # Create test user
        test_user = MongoUser(
            username='test_mongo',
            email='<EMAIL>',
            full_name='Test MongoDB User',
            role='admin'
        )
        test_user.set_password('test123')
        test_user.save()
        print("✅ Test user created successfully!")
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
```

---

## 📝 **Bước 8: Deployment với MongoDB**

### **8.1. Render.com với MongoDB Atlas**

Environment Variables trong Render:
```bash
MONGODB_URI=mongodb+srv://classroom_admin:<password>@vietnamese-classroom.xxxxx.mongodb.net/vietnamese_classroom?retryWrites=true&w=majority
SECRET_KEY=your-secret-key
FLASK_ENV=production
FLASK_DEBUG=False
```

### **8.2. Heroku với MongoDB Atlas**

```bash
heroku config:set MONGODB_URI="mongodb+srv://classroom_admin:<password>@vietnamese-classroom.xxxxx.mongodb.net/vietnamese_classroom?retryWrites=true&w=majority"
```

---

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **Connection timeout**:
   - Kiểm tra IP whitelist trong MongoDB Atlas
   - Verify connection string

2. **Authentication failed**:
   - Kiểm tra username/password
   - Verify database user permissions

3. **SSL Certificate issues**:
   - Add `ssl_cert_reqs=ssl.CERT_NONE` trong MONGODB_SETTINGS

---

## 📞 **Support**

Nếu gặp vấn đề:
1. Check MongoDB Atlas logs
2. Verify connection string format
3. Test với MongoDB Compass GUI tool
4. Check Flask app logs

**🎉 Chúc mừng! Bạn đã setup MongoDB thành công!**
