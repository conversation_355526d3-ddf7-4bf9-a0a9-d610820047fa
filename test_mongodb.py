#!/usr/bin/env python3
"""
Test MongoDB connection for Vietnamese Classroom Management System
"""

import os
import sys
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.mongo_models import MongoUser, MongoClass, MongoStudent

def test_mongodb_connection():
    """Test MongoDB connection and basic operations"""
    print("🍃 Testing MongoDB Connection...")
    print("=" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # Test 1: Basic connection
            print("1️⃣  Testing basic connection...")
            user_count = MongoUser.objects.count()
            print(f"   ✅ Connected! Current users in database: {user_count}")
            
            # Test 2: Create test user
            print("\n2️⃣  Testing user creation...")
            test_username = f"test_mongo_{int(datetime.now().timestamp())}"
            
            test_user = MongoUser(
                username=test_username,
                email=f"{test_username}@test.com",
                full_name="Test MongoDB User",
                role="admin"
            )
            test_user.set_password("test123")
            test_user.save()
            print(f"   ✅ Test user created: {test_username}")
            
            # Test 3: Query test user
            print("\n3️⃣  Testing user query...")
            found_user = MongoUser.objects(username=test_username).first()
            if found_user:
                print(f"   ✅ User found: {found_user.full_name} ({found_user.role})")
                print(f"   ✅ Password check: {found_user.check_password('test123')}")
            else:
                print("   ❌ User not found")
                return False
            
            # Test 4: Create test class
            print("\n4️⃣  Testing class creation...")
            test_class = MongoClass(
                name="Test Class MongoDB",
                description="Test class for MongoDB connection",
                manager=found_user,
                is_active=True
            )
            test_class.save()
            print(f"   ✅ Test class created: {test_class.name}")
            
            # Test 5: Query with relationships
            print("\n5️⃣  Testing relationships...")
            classes_by_manager = MongoClass.objects(manager=found_user)
            print(f"   ✅ Classes managed by {found_user.username}: {classes_by_manager.count()}")
            
            # Test 6: Update operations
            print("\n6️⃣  Testing update operations...")
            found_user.phone = "0123456789"
            found_user.save()
            updated_user = MongoUser.objects(username=test_username).first()
            print(f"   ✅ User phone updated: {updated_user.phone}")
            
            # Test 7: Delete operations
            print("\n7️⃣  Testing delete operations...")
            test_class.delete()
            found_user.delete()
            print("   ✅ Test data cleaned up")
            
            print("\n" + "=" * 50)
            print("🎉 All MongoDB tests passed!")
            print("\n📊 Database Statistics:")
            print(f"   Users: {MongoUser.objects.count()}")
            print(f"   Classes: {MongoClass.objects.count()}")
            print(f"   Students: {MongoStudent.objects.count()}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ MongoDB test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_connection_string():
    """Test the specific connection string"""
    print("\n🔗 Testing Connection String...")
    print("=" * 50)

    connection_string = "mongodb+srv://root:<EMAIL>/vietnamese_classroom"
    print(f"Connection: {connection_string}")

    try:
        import pymongo
        import ssl

        # Create client with SSL settings
        client = pymongo.MongoClient(
            connection_string,
            serverSelectionTimeoutMS=10000,
            tlsAllowInvalidCertificates=True
        )

        # Test connection
        client.admin.command('ping')
        print("   ✅ Connection string is valid")

        # List databases
        db_list = client.list_database_names()
        print(f"   ✅ Available databases: {db_list}")

        # Test database access
        db = client.vietnamese_classroom
        collections = db.list_collection_names()
        print(f"   ✅ Collections in vietnamese_classroom: {collections}")

        client.close()
        return True

    except Exception as e:
        print(f"   ❌ Connection string test failed: {e}")
        return False

def show_environment_info():
    """Show environment information"""
    print("\n🔧 Environment Information...")
    print("=" * 50)
    
    print(f"Python version: {sys.version}")
    
    try:
        import pymongo
        print(f"PyMongo version: {pymongo.version}")
    except ImportError:
        print("❌ PyMongo not installed")
    
    try:
        import mongoengine
        print(f"MongoEngine version: {mongoengine.__version__}")
    except ImportError:
        print("❌ MongoEngine not installed")
    
    try:
        import flask_mongoengine
        print(f"Flask-MongoEngine version: {flask_mongoengine.__version__}")
    except ImportError:
        print("❌ Flask-MongoEngine not installed")
    
    # Check environment variables
    mongodb_uri = os.environ.get('MONGODB_URI')
    if mongodb_uri:
        # Hide password for security
        safe_uri = mongodb_uri.replace(':12345@', ':****@')
        print(f"MONGODB_URI: {safe_uri}")
    else:
        print("❌ MONGODB_URI not set in environment")

if __name__ == '__main__':
    print("🍃 Vietnamese Classroom Management - MongoDB Connection Test")
    print("=" * 60)
    
    # Show environment info
    show_environment_info()
    
    # Test connection string
    connection_success = test_connection_string()
    
    if connection_success:
        # Test MongoDB operations
        test_success = test_mongodb_connection()
        
        if test_success:
            print("\n✅ MongoDB is ready for use!")
            print("\n💡 Next steps:")
            print("   1. Run migration: python migrate_to_mongodb.py")
            print("   2. Start the application: python run.py")
            print("   3. Test the web interface")
        else:
            print("\n❌ MongoDB operations failed")
            sys.exit(1)
    else:
        print("\n❌ Connection string test failed")
        print("\n🔧 Troubleshooting:")
        print("   1. Check your internet connection")
        print("   2. Verify MongoDB Atlas cluster is running")
        print("   3. Check username/password in connection string")
        print("   4. Verify IP whitelist in MongoDB Atlas")
        sys.exit(1)
