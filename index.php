<?php
/**
 * Main Entry Point
 * Vietnamese Classroom Management System - PHP Version
 */

session_start();

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include configuration and authentication
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/helpers.php';

// Check if user is logged in
if (!isLoggedIn()) {
    // Show login page
    include __DIR__ . '/auth/login.php';
    exit;
}

// Get current user
$current_user = getCurrentUser();
if (!$current_user) {
    logout();
    header('Location: /');
    exit;
}

// Route handling
$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? 'index';

// Define allowed pages based on user role
$allowed_pages = [
    'admin' => ['dashboard', 'users', 'classes', 'students', 'schedules', 'attendance', 'events', 'finances', 'expenses', 'reports'],
    'manager' => ['dashboard', 'classes', 'students', 'schedules', 'attendance', 'events', 'finances', 'reports'],
    'teacher' => ['dashboard', 'schedules', 'attendance', 'students'],
    'user' => ['dashboard']
];

// Check if user has permission to access the page
if (!in_array($page, $allowed_pages[$current_user['role']])) {
    $page = 'dashboard';
}

?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hệ thống Quản lý Lớp học Tiếng Việt</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f97316',
                        secondary: '#fb923c'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg border-b-2 border-primary">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-primary">QLLH Tiếng Việt</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="?page=dashboard" class="<?= $page === 'dashboard' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                        </a>
                        
                        <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
                        <a href="?page=classes" class="<?= $page === 'classes' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-chalkboard mr-2"></i>Lớp học
                        </a>
                        <a href="?page=students" class="<?= $page === 'students' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-user-graduate mr-2"></i>Học sinh
                        </a>
                        <a href="?page=schedules" class="<?= $page === 'schedules' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-calendar-alt mr-2"></i>Lịch học
                        </a>
                        <?php endif; ?>
                        
                        <a href="?page=attendance" class="<?= $page === 'attendance' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-check-circle mr-2"></i>Điểm danh
                        </a>
                        
                        <?php if (in_array($current_user['role'], ['admin', 'manager'])): ?>
                        <a href="?page=finances" class="<?= $page === 'finances' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-money-bill-wave mr-2"></i>Tài chính
                        </a>
                        <?php endif; ?>
                        
                        <?php if ($current_user['role'] === 'admin'): ?>
                        <a href="?page=users" class="<?= $page === 'users' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-users mr-2"></i>Người dùng
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="relative">
                            <button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="user-menu-button">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                                    <span class="text-white text-sm font-medium"><?= strtoupper(substr($current_user['full_name'], 0, 1)) ?></span>
                                </div>
                            </button>
                            <div class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5" id="user-menu">
                                <div class="py-1">
                                    <div class="px-4 py-2 text-sm text-gray-700 border-b">
                                        <div class="font-medium"><?= htmlspecialchars($current_user['full_name']) ?></div>
                                        <div class="text-gray-500"><?= htmlspecialchars($current_user['email']) ?></div>
                                    </div>
                                    <a href="?page=profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user mr-2"></i>Hồ sơ
                                    </a>
                                    <a href="?action=logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Đăng xuất
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <?php
        // Handle logout
        if ($action === 'logout') {
            logout();
            header('Location: /');
            exit;
        }

        // Include the appropriate page
        $page_file = __DIR__ . "/pages/{$page}.php";
        if (file_exists($page_file)) {
            include $page_file;
        } else {
            include __DIR__ . '/pages/dashboard.php';
        }
        ?>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-sm text-gray-500">
                © 2025 Hệ thống Quản lý Lớp học Tiếng Việt. Phát triển bởi QLLHTTBB.
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // User menu toggle
        document.getElementById('user-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const button = document.getElementById('user-menu-button');
            const menu = document.getElementById('user-menu');
            
            if (!button.contains(event.target) && !menu.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });

        // Notification system
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 
                type === 'error' ? 'bg-red-500 text-white' : 
                'bg-blue-500 text-white'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Show PHP notifications
        <?php if (isset($_SESSION['notification'])): ?>
        showNotification('<?= addslashes($_SESSION['notification']['message']) ?>', '<?= $_SESSION['notification']['type'] ?>');
        <?php unset($_SESSION['notification']); ?>
        <?php endif; ?>
    </script>
</body>
</html>
