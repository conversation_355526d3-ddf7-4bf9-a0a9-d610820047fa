<?php
/**
 * Database Setup Script
 * Vietnamese Classroom Management System
 */

echo "🗄️  Database Setup for Vietnamese Classroom Management System\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Get database credentials from user
echo "📋 Please enter your MySQL credentials:\n";

echo "MySQL Host [localhost]: ";
$host = trim(fgets(STDIN));
if (empty($host)) $host = 'localhost';

echo "MySQL Username [root]: ";
$username = trim(fgets(STDIN));
if (empty($username)) $username = 'root';

echo "MySQL Password: ";
$password = trim(fgets(STDIN));

echo "Database Name [vietnamese_classroom]: ";
$db_name = trim(fgets(STDIN));
if (empty($db_name)) $db_name = 'vietnamese_classroom';

echo "\n🔄 Testing connection...\n";

try {
    // Test connection without database
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ MySQL connection successful!\n\n";
    
    // Create database if not exists
    echo "🗄️  Creating database '$db_name'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$db_name' created/verified!\n\n";
    
    // Connect to the specific database
    $dsn = "mysql:host=$host;dbname=$db_name;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // Import schema
    echo "📥 Importing database schema...\n";
    $schema = file_get_contents('database/schema.sql');
    
    if ($schema === false) {
        throw new Exception("Could not read database/schema.sql");
    }
    
    // Split SQL statements
    $statements = array_filter(
        array_map('trim', explode(';', $schema)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $success_count = 0;
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                $success_count++;
            } catch (PDOException $e) {
                // Skip errors for existing tables, etc.
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "⚠️  Warning: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "✅ Schema imported successfully! ($success_count statements executed)\n\n";
    
    // Update config file
    echo "⚙️  Updating configuration file...\n";
    
    $config_content = "<?php
/**
 * Database Configuration - Auto-generated
 * Vietnamese Classroom Management System
 */

// Set environment variables for database connection
\$_ENV['DB_HOST'] = '$host';
\$_ENV['DB_NAME'] = '$db_name';
\$_ENV['DB_USER'] = '$username';
\$_ENV['DB_PASS'] = '$password';

" . file_get_contents('config/database.php');
    
    file_put_contents('config/database.php', $config_content);
    echo "✅ Configuration updated!\n\n";
    
    // Test the application
    echo "🧪 Testing application...\n";
    
    // Check if admin user exists
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "👤 Creating default admin user...\n";
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, full_name, role, is_active) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute(['admin', '<EMAIL>', $admin_password, 'Administrator', 'admin', 1]);
        echo "✅ Admin user created!\n";
        echo "   Username: admin\n";
        echo "   Password: admin123\n\n";
    } else {
        echo "✅ Admin user already exists!\n\n";
    }
    
    echo "🎉 Database setup completed successfully!\n\n";
    echo "📋 Next steps:\n";
    echo "1. Start PHP server: php -S localhost:8000\n";
    echo "2. Open browser: http://localhost:8000\n";
    echo "3. Login with: admin / admin123\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\n🔧 Troubleshooting:\n";
    echo "1. Make sure MySQL is running\n";
    echo "2. Check your credentials\n";
    echo "3. Ensure user has CREATE DATABASE privileges\n";
    exit(1);
}
?>
